# -*- coding: utf-8 -*-
"""
测试Chrome配置修复
验证新的Chrome配置是否能正常工作
"""

import os
import sys
from selenium import webdriver
from selenium.webdriver.chrome.options import Options

def test_chrome_config():
    """测试Chrome配置"""
    print("🔧 测试Chrome浏览器配置...")
    
    try:
        # 使用本地Chrome浏览器路径
        chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe")
        ]

        chrome_binary = None
        for path in chrome_paths:
            if os.path.exists(path):
                chrome_binary = path
                print(f"✅ 找到Chrome浏览器: {path}")
                break

        if not chrome_binary:
            print("❌ 未找到本地Chrome浏览器")
            return False

        # 使用修复后的Chrome配置
        chrome_options = Options()
        chrome_options.binary_location = chrome_binary
        
        # 基本反检测设置 - 移除了有问题的excludeSwitches
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_experimental_option('useAutomationExtension', False)
        
        # 设置用户代理
        user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        chrome_options.add_argument(f'--user-agent={user_agent}')
        
        # 其他基本设置
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--disable-extensions')

        print("🚀 正在启动Chrome浏览器...")
        driver = webdriver.Chrome(options=chrome_options)
        
        print("✅ Chrome浏览器启动成功！")
        
        # 执行反检测脚本
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        # 测试访问页面
        print("🌐 测试访问百度...")
        driver.get("https://www.baidu.com")
        
        print(f"📄 页面标题: {driver.title}")
        print("✅ 页面访问成功！")
        
        # 关闭浏览器
        driver.quit()
        print("✅ 测试完成，Chrome配置正常工作！")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("Chrome配置修复测试")
    print("=" * 50)
    
    success = test_chrome_config()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 测试成功！Chrome配置已修复")
        print("现在可以正常运行智慧中小学登录程序了")
    else:
        print("❌ 测试失败，请检查Chrome安装或联系技术支持")
    print("=" * 50)
