2025-07-24 02:27:26,680 - INFO - ==================================================
2025-07-24 02:27:26,681 - INFO - 智慧中小学自动登录脚本启动
2025-07-24 02:27:26,681 - INFO - 配置信息 - 用户名: 15629266999
2025-07-24 02:27:26,681 - INFO - 配置信息 - 登录URL: https://auth.smartedu.cn/uias/login
2025-07-24 02:27:26,681 - INFO - 配置信息 - 目标URL: https://basic.smartedu.cn/training/2025sqpx
2025-07-24 02:27:26,681 - INFO - ==================================================
2025-07-24 02:27:26,681 - INFO - 开始执行自动登录流程...
2025-07-24 02:27:26,681 - INFO - 正在初始化Chrome浏览器...
2025-07-24 02:27:29,817 - INFO - Chrome浏览器初始化成功
2025-07-24 02:27:29,817 - INFO - 正在访问登录页面: https://auth.smartedu.cn/uias/login
2025-07-24 02:27:30,860 - INFO - 登录页面加载成功
2025-07-24 02:27:32,716 - INFO - 开始填写登录表单...
2025-07-24 02:27:34,275 - INFO - 用户名输入完成
2025-07-24 02:27:36,673 - INFO - 密码输入完成
2025-07-24 02:27:37,674 - INFO - 已勾选同意协议
2025-07-24 02:27:38,269 - INFO - 点击登录按钮...
2025-07-24 02:27:39,159 - INFO - 登录按钮已点击
2025-07-24 02:27:39,160 - INFO - 等待验证码加载...
2025-07-24 02:27:42,187 - INFO - 检测到腾讯验证码元素: #tcaptcha_iframe_dy
2025-07-24 02:27:42,187 - INFO - 验证码已加载
2025-07-24 02:27:42,187 - INFO - 验证码处理尝试 1/3
2025-07-24 02:27:42,188 - INFO - 正在获取腾讯验证码图片...
2025-07-24 02:27:42,188 - INFO - 等待腾讯验证码iframe加载...
2025-07-24 02:27:47,201 - INFO - 找到验证码iframe，使用选择器: #tcaptcha_iframe_dy
2025-07-24 02:27:47,220 - INFO - 已切换到验证码iframe
2025-07-24 02:27:50,247 - INFO - 在iframe中找到 3 个带背景图的div元素
2025-07-24 02:27:50,264 - INFO - 从tc-bg-img获取到背景图片
2025-07-24 02:27:50,279 - INFO - 从tc-fg-item获取到滑块图片
2025-07-24 02:27:50,291 - INFO - 从tc-fg-item获取到滑块图片
2025-07-24 02:27:50,293 - INFO - 背景图片URL: https://turing.captcha.qcloud.com/cap_union_new_getcapbysig?img_index=1&image=02610900005c320a000000
2025-07-24 02:27:50,293 - INFO - 滑块图片URL: https://turing.captcha.qcloud.com/cap_union_new_getcapbysig?img_index=0&image=02610900005c320a000000
2025-07-24 02:27:50,620 - INFO - 图片下载并编码成功，大小: 25635 bytes
2025-07-24 02:27:50,936 - INFO - 图片下载并编码成功，大小: 13068 bytes
2025-07-24 02:27:50,938 - INFO - 正在调用第三方API识别验证码...
2025-07-24 02:27:51,867 - INFO - 验证码识别API响应: {'msg': '识别成功', 'code': 10000, 'data': {'code': 0, 'data': '2', 'time': 0.4387633800506592, 'unique_code': 'e63666a4af7a467ad0367f17e68118b7'}}
2025-07-24 02:27:51,868 - WARNING - 识别的坐标值过小: 2，可能识别错误，使用调整值
2025-07-24 02:27:51,868 - INFO - 验证码识别成功，调整后X坐标: 100
2025-07-24 02:27:51,868 - INFO - 开始执行腾讯验证码滑块拖拽，目标位置: 100
2025-07-24 02:27:51,903 - INFO - 切换到验证码iframe查找滑块
2025-07-24 02:27:51,914 - INFO - 在iframe中找到滑块元素: .tc-slider-normal
2025-07-24 02:27:51,923 - INFO - 滑块当前位置: 28.75, 宽度: 54
2025-07-24 02:27:51,961 - INFO - 验证码容器宽度: 300
2025-07-24 02:27:51,961 - INFO - 原始坐标: 100, 调整后的移动距离: 93.75
2025-07-24 02:27:51,961 - INFO - 尝试方法1: 真实鼠标按住拖拽...
2025-07-24 02:28:01,941 - WARNING - 方法1失败，移动距离: 0.0 像素
2025-07-24 02:28:01,942 - INFO - 尝试方法2: 分段拖拽...
2025-07-24 02:28:06,986 - WARNING - 方法2失败，移动距离: 0.0 像素
2025-07-24 02:28:06,987 - WARNING - ⚠️ 自动拖拽方法都失败了
2025-07-24 02:28:06,987 - INFO - 🔧 建议解决方案：
2025-07-24 02:28:06,987 - INFO - 1. 手动完成验证码拖拽
2025-07-24 02:28:06,987 - INFO - 2. 或者尝试更换验证码识别服务
2025-07-24 02:28:06,987 - INFO - 3. 或者联系验证码服务提供商获取更好的识别结果
2025-07-24 02:28:06,988 - INFO - ⏰ 给您30秒时间手动完成验证码拖拽...
2025-07-24 02:28:07,989 - INFO - ⏰ 还有 30 秒...
2025-07-24 02:28:13,063 - INFO - ⏰ 还有 25 秒...
2025-07-24 02:28:14,090 - INFO - ✅ 检测到验证码已完成！
2025-07-24 02:28:14,091 - INFO - 🎉 手动验证码完成成功！
2025-07-24 02:28:14,316 - INFO - 滑块拖拽成功完成
2025-07-24 02:28:14,317 - INFO - 滑块拖拽操作完成
2025-07-24 02:28:17,319 - INFO - 检查登录状态...
2025-07-24 02:28:20,329 - INFO - 当前页面URL: https://www.smartedu.cn/
2025-07-24 02:28:20,329 - INFO - 登录成功，已跳转到其他页面
2025-07-24 02:28:20,329 - INFO - 验证码处理成功
2025-07-24 02:28:20,330 - INFO - 等待登录完成...
2025-07-24 02:28:23,331 - INFO - 正在访问目标页面: https://basic.smartedu.cn/training/2025sqpx
2025-07-24 02:28:29,605 - INFO - 目标页面加载完成，当前URL: https://basic.smartedu.cn/training/2025sqpx
2025-07-24 02:28:29,606 - INFO - 开始截图...
2025-07-24 02:28:29,613 - INFO - 页面高度: 2804, 窗口高度: 788
2025-07-24 02:28:32,362 - INFO - 截图保存成功: screenshots/screenshot_upper_20250724_022832.png
2025-07-24 02:28:32,364 - INFO - 任务完成！截图已保存: screenshots/screenshot_upper_20250724_022832.png
2025-07-24 02:28:32,364 - INFO - 正在关闭浏览器...
2025-07-24 02:28:35,628 - INFO - 浏览器已关闭
