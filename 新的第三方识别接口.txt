账号：573749877
密码：Lliuyaxin


对接文档：
HTTP接口（同时支持HTTPS）
注意：所有HTTP接口提交方式均为POST提交。

快速入门
1、会员注册及登录
记录"账号"、"密码"，用于接口接入时使用
2、接口接入
参考下面的各个接口说明，选择一个最合适您的使用即可；
如果您在接入过程中有困难或疑问，欢迎咨询客服，辅助您完成接入。
*注意:
图片大小不要超过3MB
http超时时间根据语言设置成60秒。否则因为超时时间不够造成得主动断开http连接，识别明细会出现"请延长超时时间"一样扣费。
一.通用图片识别接口
接口地址	说明	Content-Type	图片格式	返回格式
http://api.ttshitu.com/predict	模拟表单提交	multipart/form-data;charset=UTF-8	Byte[] 字节流	JSON
PostData 为JSON格式	application/json;charset=UTF-8	Base64 编码	JSON
请求：

参数	说明	必填	类型
username	用户名	是	string
password	密码	是	string
typeid	默认 3 数英混合
参数	说明
1	纯数字
1001	纯数字2
2	纯英文
1002	纯英文2
3	数英混合
1003	数英混合2
4	闪动GIF
7	无感学习(独家)
66	问答题
11	计算题
1005	快速计算题
5	快速计算题2
16	汉字
32	通用文字识别(证件、单据)
29	旋转类型
1029	背景匹配旋转类型(需要2张图 一张中间的小图一张背景图)
2029	背景匹配双旋转类型(需要2张图 一张中间的小图一张背景图)
19	点选1个坐标
20	点选3个坐标
21	点选3 ~ 5个坐标
22	点选5 ~ 8个坐标
27	点选1 ~ 4个坐标
48	轨迹类型
18	缺口识别（需要2张图 一张目标图一张缺口图）
33	单缺口识别（返回X轴坐标 只需要1张图）
34	缺口识别2（返回X轴坐标 只需要1张图）
3400	缺口识别（返回缺口左上角X,Y坐标 只需要1张图）
53	拼图识别
否	string
angle	旋转角度：当typeid为14时旋转角度 默认90	否	string
step	每次旋转的角度：当typeid为旋转类型时每次旋转的角度 默认为10。	否	string
typename	无感学习子类型名称(可为空)：用户自定义（需自己记住,不同时为不同的无感学习）。typeid为(7: 无感学习)时传	否	string(30个字符)
remark	备注字段 如：填写计算结果 (兼容unicode) 遇到中文乱码情况 请unicode编码以免造成错误。	否	string
image	byte[] 原始图二进制数据。	是	byte[]
base64图片（注意不含：data:image/jpg;base64,直接图片base64编码）。	是	string
imageback(缺口识别2张图传背景图需要,1029,2029类型传背景图片需要)	byte[] 原始图二进制数据。	否	byte[]
base64图片（注意不含：data:image/jpg;base64,直接图片base64编码）。	否	string
content(快速点选需要)	标题内容 如：填写 "你好"中文请unicode编码以免造成错误。	否	string
title_image(快速点选需要)	byte[] 原始图二进制数据。	否	byte[]
base64图片（注意不含：data:image/jpg;base64,直接图片base64编码）。	否	string
返回：

参数	说明	类型
success	请求返回的状态,true成功，false失败。注：接入的程序只需根据此字段判断请求是否成功。	boolean
code	返回的code。成功为0，失败为-1	string
message	当请求失败时，返回的失败原因即success=false时返回的原因	string
data	成功返回的结果内容。具体包含下面参数	-
├ result	当请求成功时，返回的识别的结果,即success=ture时返回的识别结果如:AXSZ	string
└ id	当请求成功时，返回的识别的结果,即success=ture时返回的识别结果的id用于报错	string
请求示例：HTTP

    

    POST /predict HTTP/1.1
    Host: api.ttshitu.com
    Content-Type: application/json
    {
        "username": "****",
        "password": "****",
        "typeid": "3",
        "image": "图片base64"//其他参数根据需要对应文档添加
    }
    

    
返回示例：

成功
    

    {
      "success": true,
      "code": "0",
      "message": "success",
      "data": {
        "result": "hhum",
        "id": "00504808e68a41ad83ab5c1e6367ae6b"
       }
    }
    

    
失败
    

    {
       "success": false,
       "code": "-1",
       "message": "用户名或密码错误",
       "data": ""
    }
    

    
二.报错接口
注意：注:为了保障接口使用的流畅性,报错结果在5min后批量更新，并返还次数或金额。

接口地址	说明	返回数据格式
http://api.ttshitu.com/reporterror.json	模拟表单提交或JSON格式提交	JSON
请求：

参数	说明	必填	类型
id	识别成功返回的id	是	string
返回：

参数	说明	类型
success	请求返回的状态,true成功，false失败。注：接入的程序只需根据此字段判断请求是否成功。	boolean
code	返回的code。成功为0，失败为-1	string
message	当请求失败时，返回的失败原因即success=false时返回的原因	string
data	结果载体，此时为空	-
返回示例：

成功
    

    {
        "success": true,
        "code": "0",
        "message": "success",
        "data": {
            "result": "报错成功"
        }
    }
    

    
失败
    

    {
       "success": false,
       "code": "-1",
       "message": "不存在的报错ID，或已经超过允许报错的时间范围",
       "data": ""
    }
    

    
三.余额查询接口(注意:该接口为GET请求)
接口地址	说明	返回数据格式
http://api.ttshitu.com/queryAccountInfo.json	Get请求	JSON
请求：http://api.ttshitu.com/queryAccountInfo.json?username=****&password=***

参数	说明	必填	类型
username	用户名	是	string
password	密码	是	string
返回：

参数	说明	类型
success	请求返回的状态,true成功，false失败。注：接入的程序只需根据此字段判断请求是否成功。	boolean
code	返回的code。成功为0，失败为-1	string
message	当请求失败时，返回的失败原因即success=false时返回的原因	string
data	结果载体，此时为空	-
返回示例：

成功
    

    {
        "success": true,
        "code": "0",
        "message": "success",
        "data": {
            "balance": "0.156",//注:实时余额
            "consumed": "0.844",//注:实时总消费
            "successNum": "557",//注:实时总识别成功的次数
            "failNum": "0"//注:实时总识别错误数
        }
    }
    

    
失败
    

    {
        "success": false,
        "code": "-1",
        "message": "[dsd12], 用户被禁用!",
        "data": ""
    }
    
示例：
import base64
import json
import requests
# 一、图片文字类型(默认 3 数英混合)：
# 1 : 纯数字
# 1001：纯数字2
# 2 : 纯英文
# 1002：纯英文2
# 3 : 数英混合
# 1003：数英混合2
#  4 : 闪动GIF
# 7 : 无感学习(独家)
# 11 : 计算题
# 1005:  快速计算题
# 16 : 汉字
# 32 : 通用文字识别(证件、单据)
# 66:  问答题
# 49 :recaptcha图片识别
# 二、图片旋转角度类型：
# 29 :  旋转类型
# 1029 :  背景匹配旋转类型 注意：中间小图传到image中，背景图传到imageback 中 imageback模仿image 添加
# 2029 :  背景匹配双旋转类型 注意：中间小图传到image中，背景图传到imageback 中  imageback模仿image 添加
#
# 三、图片坐标点选类型：
# 19 :  1个坐标
# 20 :  3个坐标
# 21 :  3 ~ 5个坐标
# 22 :  5 ~ 8个坐标
# 27 :  1 ~ 4个坐标
# 48 : 轨迹类型
#
# 四、缺口识别
# 18 : 缺口识别（需要2张图 一张目标图一张缺口图）
# 33 : 单缺口识别（返回X轴坐标 只需要1张图）
# 34 : 缺口识别2（返回X轴坐标 只需要1张图）
# 五、拼图识别
# 53：拼图识别
def base64_api(uname, pwd, img, typeid):
    with open(img, 'rb') as f:
        base64_data = base64.b64encode(f.read())
        b64 = base64_data.decode()
    data = {"username": uname, "password": pwd, "typeid": typeid, "image": b64}
    result = json.loads(requests.post("http://api.ttshitu.com/predict", json=data).text)
    if result['success']:
        return result["data"]["result"]
    else:
        #！！！！！！！注意：返回 人工不足等 错误情况 请加逻辑处理防止脚本卡死 继续重新 识别
        return result["message"]
    return ""


if __name__ == "__main__":
    img_path = "C:/Users/<USER>/Desktop/file.jpg"
    result = base64_api(uname='你的账号', pwd='你的密码', img=img_path, typeid=3)
    print(result)
        

    
报错脚本
        

import json
import requests

def reportError(id):
    data = {"id": id}
    result = json.loads(requests.post("http://api.ttshitu.com/reporterror.json", json=data).text)
    if result['success']:
        return "报错成功"
    else:
        return result["message"]
    return ""

if __name__ == "__main__":
    result = reportError(id='成功返回的id')
    print(result)
        
    