2025-07-24 02:50:25,921 - INFO - ==================================================
2025-07-24 02:50:25,921 - INFO - 智慧中小学自动登录脚本启动
2025-07-24 02:50:25,921 - INFO - 配置信息 - 用户名: 15629266999
2025-07-24 02:50:25,921 - INFO - 配置信息 - 登录URL: https://auth.smartedu.cn/uias/login
2025-07-24 02:50:25,921 - INFO - 配置信息 - 目标URL: https://basic.smartedu.cn/training/2025sqpx
2025-07-24 02:50:25,922 - INFO - ==================================================
2025-07-24 02:50:25,922 - INFO - 开始执行自动登录流程...
2025-07-24 02:50:25,922 - INFO - 正在初始化Chrome浏览器...
2025-07-24 02:50:29,053 - INFO - Chrome浏览器初始化成功
2025-07-24 02:50:29,054 - INFO - 正在访问登录页面: https://auth.smartedu.cn/uias/login
2025-07-24 02:50:29,919 - INFO - 登录页面加载成功
2025-07-24 02:50:31,829 - INFO - 开始填写登录表单...
2025-07-24 02:50:33,412 - INFO - 用户名输入完成
2025-07-24 02:50:35,525 - INFO - 密码输入完成
2025-07-24 02:50:36,455 - INFO - 已勾选同意协议
2025-07-24 02:50:36,996 - INFO - 点击登录按钮...
2025-07-24 02:50:37,819 - INFO - 登录按钮已点击
2025-07-24 02:50:37,819 - INFO - 等待验证码加载...
2025-07-24 02:50:40,840 - INFO - 检测到腾讯验证码元素: #tcaptcha_iframe_dy
2025-07-24 02:50:40,840 - INFO - 验证码已加载
2025-07-24 02:50:40,840 - INFO - 验证码处理尝试 1/3
2025-07-24 02:50:40,841 - INFO - 正在获取腾讯验证码图片...
2025-07-24 02:50:40,841 - INFO - 等待腾讯验证码iframe加载...
2025-07-24 02:50:45,868 - INFO - 找到验证码iframe，使用选择器: #tcaptcha_iframe_dy
2025-07-24 02:50:45,953 - INFO - 已切换到验证码iframe
2025-07-24 02:50:48,973 - INFO - 在iframe中找到 3 个带背景图的div元素
2025-07-24 02:50:49,003 - INFO - 从tc-bg-img获取到背景图片
2025-07-24 02:50:49,045 - INFO - 从tc-fg-item获取到滑块图片
2025-07-24 02:50:49,091 - INFO - 从tc-fg-item获取到滑块图片
2025-07-24 02:50:49,094 - INFO - 背景图片URL: https://turing.captcha.qcloud.com/cap_union_new_getcapbysig?img_index=1&image=026109000031375a000000
2025-07-24 02:50:49,094 - INFO - 滑块图片URL: https://turing.captcha.qcloud.com/cap_union_new_getcapbysig?img_index=0&image=026109000031375a000000
2025-07-24 02:50:49,829 - INFO - 图片下载并编码成功，大小: 44508 bytes
2025-07-24 02:50:50,398 - INFO - 图片下载并编码成功，大小: 30988 bytes
2025-07-24 02:50:50,400 - INFO - 正在调用第三方API识别验证码...
2025-07-24 02:50:50,666 - INFO - 验证码识别API响应: {'success': True, 'code': '0', 'message': 'success', 'data': {'result': '486', 'id': 'ej7YM5hcRQu-5ASZJPxQog'}}
2025-07-24 02:50:50,666 - WARNING - 识别的坐标值过大: 486，进行调整
2025-07-24 02:50:50,667 - INFO - 验证码识别成功，调整后X坐标: 300
2025-07-24 02:50:50,667 - INFO - 开始执行腾讯验证码滑块拖拽，目标位置: 300
2025-07-24 02:50:50,736 - INFO - 切换到验证码iframe查找滑块
2025-07-24 02:50:50,748 - INFO - 在iframe中找到滑块元素: .tc-slider-normal
2025-07-24 02:50:50,766 - INFO - 滑块当前位置: 28.75, 宽度: 54
2025-07-24 02:50:50,817 - INFO - 验证码容器宽度: 300
2025-07-24 02:50:50,817 - INFO - 原始坐标: 300, 调整后的移动距离: 281.25
2025-07-24 02:50:50,817 - INFO - 尝试方法1: 真实鼠标按住拖拽...
2025-07-24 02:51:01,052 - WARNING - 方法1失败，移动距离: 0.0 像素
2025-07-24 02:51:01,052 - INFO - 尝试方法2: 分段拖拽...
2025-07-24 02:51:06,116 - WARNING - 方法2失败，移动距离: 0.0 像素
2025-07-24 02:51:06,116 - WARNING - ⚠️ 自动拖拽方法都失败了
2025-07-24 02:51:06,116 - INFO - 🔧 建议解决方案：
2025-07-24 02:51:06,117 - INFO - 1. 手动完成验证码拖拽
2025-07-24 02:51:06,117 - INFO - 2. 或者尝试更换验证码识别服务
2025-07-24 02:51:06,117 - INFO - 3. 或者联系验证码服务提供商获取更好的识别结果
2025-07-24 02:51:06,117 - INFO - ⏰ 给您30秒时间手动完成验证码拖拽...
2025-07-24 02:51:07,118 - INFO - ⏰ 还有 30 秒...
2025-07-24 02:51:12,181 - INFO - ⏰ 还有 25 秒...
2025-07-24 02:51:17,253 - INFO - ⏰ 还有 20 秒...
2025-07-24 02:51:22,371 - INFO - ⏰ 还有 15 秒...
2025-07-24 02:51:27,429 - INFO - ⏰ 还有 10 秒...
2025-07-24 02:51:31,706 - INFO - 正在关闭浏览器...
2025-07-24 02:51:31,711 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))': /session/d6bf345413a1ba6c6f98b6c543192b6d
2025-07-24 02:51:37,848 - INFO - 正在关闭浏览器...
2025-07-24 02:51:41,938 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001B07764F4A0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /session/d6bf345413a1ba6c6f98b6c543192b6d
2025-07-24 02:51:46,011 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001B07764F620>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /session/d6bf345413a1ba6c6f98b6c543192b6d
2025-07-24 02:51:50,075 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000001B07764F9E0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /session/d6bf345413a1ba6c6f98b6c543192b6d
2025-07-24 16:22:47,190 - INFO - 浏览器已关闭
