# -*- coding: utf-8 -*-
"""
创建最终发布包
将release文件夹打包成zip文件，方便分发
"""

import os
import shutil
import zipfile
from datetime import datetime
from pathlib import Path

def create_zip_package():
    """创建ZIP压缩包"""
    print("📦 创建最终发布包...")
    
    release_dir = Path("release")
    if not release_dir.exists():
        print("❌ release文件夹不存在，请先运行打包脚本")
        return False
    
    # 检查主程序是否存在
    exe_file = release_dir / "智慧中小学自动登录工具.exe"
    if not exe_file.exists():
        print("❌ 找不到主程序文件")
        return False
    
    # 创建压缩包名称（包含日期）
    date_str = datetime.now().strftime("%Y%m%d")
    zip_name = f"智慧中小学自动登录工具_v1.0_{date_str}.zip"
    
    print(f"🗜️ 正在创建压缩包：{zip_name}")
    
    # 创建ZIP文件
    with zipfile.ZipFile(zip_name, 'w', zipfile.ZIP_DEFLATED) as zipf:
        # 遍历release文件夹中的所有文件
        for file_path in release_dir.rglob('*'):
            if file_path.is_file():
                # 计算在ZIP中的相对路径
                arcname = file_path.relative_to(release_dir)
                zipf.write(file_path, arcname)
                print(f"  ✅ 已添加：{arcname}")
    
    # 获取文件大小
    zip_size = os.path.getsize(zip_name) / (1024 * 1024)  # MB
    
    print(f"\n🎉 压缩包创建成功！")
    print(f"📁 文件名：{zip_name}")
    print(f"📏 文件大小：{zip_size:.1f} MB")
    
    return True

def show_package_info():
    """显示发布包信息"""
    print("\n" + "=" * 60)
    print("📋 发布包内容说明")
    print("=" * 60)
    
    files_info = {
        "智慧中小学自动登录工具.exe": "主程序文件",
        "启动程序.bat": "启动脚本（双击运行）",
        "使用说明_EXE版.md": "EXE版本使用说明",
        "使用说明_GUI版.md": "详细使用说明",
        "README.md": "项目说明文档",
        "config_example.py": "配置文件示例",
        "accounts_example.txt": "账号文件示例",
        "requirements.txt": "依赖包列表"
    }
    
    for filename, description in files_info.items():
        print(f"📄 {filename:<35} - {description}")
    
    print("\n" + "=" * 60)
    print("🚀 使用方法：")
    print("1. 解压ZIP文件到任意文件夹")
    print("2. 双击'启动程序.bat'或'智慧中小学自动登录工具.exe'")
    print("3. 按照使用说明配置账号信息")
    print("4. 开始使用")
    print("=" * 60)

def main():
    """主函数"""
    print("=" * 60)
    print("🎁 智慧中小学自动登录工具 - 最终打包")
    print("=" * 60)
    
    if create_zip_package():
        show_package_info()
        print("\n✅ 最终发布包创建完成！")
    else:
        print("\n❌ 创建发布包失败")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
