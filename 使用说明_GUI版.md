# 智慧中小学自动登录工具 - GUI版本

## 📋 功能特点

- ✨ **现代化GUI界面** - 基于CustomTkinter的美观界面
- 👥 **批量账号管理** - 支持添加、编辑、删除多个账号
- ⏰ **智能倒计时** - 验证码操作时的可视化倒计时
- 📊 **实时日志显示** - 操作过程实时反馈
- 🎮 **灵活控制** - 支持开始、暂停、停止操作
- 📁 **批量导入** - 支持从txt文件批量导入账号
- 📸 **自动截图** - 登录成功后自动截图保存

## 🚀 快速开始

### 1. 环境准备

确保您的电脑已安装：
- Python 3.8 或更高版本
- Chrome 浏览器

### 2. 安装依赖

双击运行 `install_dependencies.bat` 自动安装所需依赖包。

### 3. 启动程序

双击运行 `start_gui.bat` 启动GUI程序。

## 📖 使用指南

### 账号管理

1. **添加账号**
   - 点击"➕ 添加账号"按钮
   - 填写姓名、账号、密码
   - 选择是否启用该账号

2. **编辑账号**
   - 在账号列表中选择要编辑的账号
   - 点击"✏️ 编辑"按钮
   - 修改账号信息

3. **删除账号**
   - 选择要删除的账号
   - 点击"🗑️ 删除"按钮
   - 确认删除操作

4. **批量导入**
   - 点击"📁 导入"按钮
   - 选择txt格式的账号文件
   - 文件格式：每行一个账号，格式为 `姓名 账号 密码`

### 执行任务

1. **设置倒计时**
   - 在右侧设置区域修改"验证码倒计时(秒)"
   - 默认为10秒，可根据需要调整

2. **开始执行**
   - 确保已添加并启用账号
   - 点击"🚀 开始执行"按钮
   - 程序将自动处理所有启用的账号

3. **验证码操作**
   - 当出现验证码时，程序会显示倒计时
   - 在倒计时期间手动完成验证码操作
   - 倒计时结束后程序自动继续

4. **控制操作**
   - **暂停/继续**：点击"⏸️ 暂停"可暂停执行，再次点击继续
   - **停止**：点击"⏹️ 停止"立即停止所有操作

### 查看结果

1. **实时日志**
   - 左下角日志区域显示详细的操作过程
   - 包括成功、失败、错误等信息

2. **执行状态**
   - 右侧状态区域显示当前执行状态
   - 进度条显示整体执行进度

3. **截图文件**
   - 成功登录的账号会自动截图
   - 截图保存在 `screenshots` 文件夹
   - 文件名格式：`姓名.png`

## ⚙️ 配置说明

### 倒计时设置
- 验证码倒计时时间可在界面右侧调整
- 建议设置10-30秒，根据个人操作速度调整

### 账号文件格式
导入的txt文件应遵循以下格式：
```
张三 13800138000 password123
李四 13900139000 mypassword
王五 13700137000 123456
```

## 🔧 故障排除

### 常见问题

1. **程序无法启动**
   - 检查Python环境是否正确安装
   - 运行 `install_dependencies.bat` 重新安装依赖

2. **Chrome浏览器问题**
   - 确保已安装Chrome浏览器
   - 程序会自动下载匹配的ChromeDriver

3. **验证码识别失败**
   - 增加倒计时时间
   - 确保在倒计时期间完成验证码操作

4. **账号登录失败**
   - 检查账号密码是否正确
   - 查看日志区域的详细错误信息

### 日志文件
- 详细日志保存在 `logs` 文件夹
- 每个账号都有独立的日志文件
- 可用于问题排查和分析

## 📞 技术支持

如遇到问题，请：
1. 查看日志文件中的详细错误信息
2. 检查网络连接是否正常
3. 确保Chrome浏览器版本较新

## 📝 更新日志

### v2.0 (GUI版本)
- ✨ 全新GUI界面
- 🎮 可视化操作控制
- ⏰ 智能倒计时功能
- 📊 实时状态显示
- 👥 完善的账号管理

### v1.0 (命令行版本)
- 基础自动登录功能
- 批量处理支持
- 截图保存功能
