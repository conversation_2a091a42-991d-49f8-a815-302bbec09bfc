@echo off
chcp 65001 >nul
echo ========================================
echo 智慧中小学自动登录工具 - 打包脚本
echo ========================================
echo.

echo 🔧 正在检查依赖...
pip show pyinstaller >nul 2>&1
if errorlevel 1 (
    echo 正在安装PyInstaller...
    pip install pyinstaller
)

echo.
echo 🧹 清理旧的构建文件...
if exist "build" rmdir /s /q "build"
if exist "dist" rmdir /s /q "dist"
if exist "*.spec" del "*.spec"

echo.
echo 🚀 开始打包程序...
echo ----------------------------------------

REM 使用spec文件进行打包，确保包含所有必要的文件
pyinstaller gui_app.spec

echo.
if exist "dist\智慧中小学自动登录工具.exe" (
    echo ✅ 打包成功！
    echo 可执行文件位置：dist\智慧中小学自动登录工具.exe
    echo.
    echo 📦 正在创建发布文件夹...
    if not exist "release" mkdir release

    REM 复制主程序
    copy "dist\智慧中小学自动登录工具.exe" "release\"

    REM 复制说明文档
    if exist "使用说明_GUI版.md" copy "使用说明_GUI版.md" "release\"
    if exist "README.md" copy "README.md" "release\"

    REM 复制配置文件示例
    if exist "config_example.py" copy "config_example.py" "release\"
    if exist "accounts_example.txt" copy "accounts_example.txt" "release\"

    REM 复制依赖安装脚本
    if exist "install_dependencies.bat" copy "install_dependencies.bat" "release\"
    if exist "requirements.txt" copy "requirements.txt" "release\"

    REM 创建启动脚本
    echo @echo off > "release\启动程序.bat"
    echo chcp 65001 ^>nul >> "release\启动程序.bat"
    echo echo 正在启动智慧中小学自动登录工具... >> "release\启动程序.bat"
    echo "智慧中小学自动登录工具.exe" >> "release\启动程序.bat"

    echo.
    echo 🎉 发布包已准备完成！
    echo 发布文件夹：release\
    echo.
    echo 📋 发布包内容：
    echo   - 智慧中小学自动登录工具.exe （主程序）
    echo   - 启动程序.bat （启动脚本）
    echo   - 使用说明_GUI版.md （使用说明）
    echo   - config_example.py （配置文件示例）
    echo   - accounts_example.txt （账号文件示例）
    echo.
) else (
    echo ❌ 打包失败！
    echo 请检查错误信息
)

pause
