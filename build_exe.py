# -*- coding: utf-8 -*-
"""
智慧中小学自动登录工具 - Python打包脚本
使用PyInstaller将程序打包成exe文件
"""

import os
import sys
import shutil
import subprocess
from pathlib import Path

def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("🚀 智慧中小学自动登录工具 - 打包脚本")
    print("=" * 60)
    print()

def check_dependencies():
    """检查并安装依赖"""
    print("🔧 检查依赖...")
    
    # 检查PyInstaller
    try:
        import PyInstaller
        print("✅ PyInstaller 已安装")
    except ImportError:
        print("📦 正在安装 PyInstaller...")
        subprocess.run([sys.executable, "-m", "pip", "install", "pyinstaller"], check=True)
        print("✅ PyInstaller 安装完成")
    
    # 检查其他依赖
    required_packages = ["customtkinter", "selenium", "Pillow", "requests"]
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"📦 正在安装 {package}...")
            subprocess.run([sys.executable, "-m", "pip", "install", package], check=True)
            print(f"✅ {package} 安装完成")

def clean_build():
    """清理构建文件"""
    print("\n🧹 清理旧的构建文件...")
    
    dirs_to_clean = ["build", "dist", "__pycache__"]
    for dir_name in dirs_to_clean:
        if os.path.exists(dir_name):
            shutil.rmtree(dir_name)
            print(f"🗑️ 已删除 {dir_name}")
    
    # 删除旧的spec文件（除了我们的gui_app.spec）
    for spec_file in Path(".").glob("*.spec"):
        if spec_file.name != "gui_app.spec":
            spec_file.unlink()
            print(f"🗑️ 已删除 {spec_file}")

def create_spec_file():
    """创建或更新spec文件"""
    print("\n📝 创建打包配置文件...")
    
    spec_content = '''# -*- mode: python ; coding: utf-8 -*-
import os

block_cipher = None

a = Analysis(
    ['gui_main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('requirements.txt', '.'),
        ('使用说明_GUI版.md', '.'),
        ('config_example.py', '.'),
    ],
    hiddenimports=[
        'customtkinter',
        'selenium',
        'PIL',
        'selenium.webdriver.chrome.service',
        'selenium.webdriver.chrome.options',
        'selenium.webdriver.common.by',
        'selenium.webdriver.support.ui',
        'selenium.webdriver.support.expected_conditions',
        'selenium.common.exceptions',
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.filedialog',
        'threading',
        'queue',
        'json',
        'datetime',
        'logging',
        'subprocess',
        'random',
        'time',
        'os',
        'sys',
        'io',
        'requests',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='智慧中小学自动登录工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='icon.ico' if os.path.exists('icon.ico') else None,
)
'''
    
    with open("gui_app.spec", "w", encoding="utf-8") as f:
        f.write(spec_content)
    
    print("✅ 打包配置文件已创建")

def build_exe():
    """构建exe文件"""
    print("\n🚀 开始打包程序...")
    print("-" * 40)
    
    try:
        # 使用spec文件进行打包
        result = subprocess.run([
            sys.executable, "-m", "PyInstaller", 
            "gui_app.spec"
        ], check=True, capture_output=True, text=True)
        
        print("✅ 打包完成！")
        return True
        
    except subprocess.CalledProcessError as e:
        print("❌ 打包失败！")
        print("错误信息：")
        print(e.stdout)
        print(e.stderr)
        return False

def create_release():
    """创建发布包"""
    print("\n📦 创建发布包...")
    
    exe_path = Path("dist/智慧中小学自动登录工具.exe")
    if not exe_path.exists():
        print("❌ 找不到生成的exe文件")
        return False
    
    # 创建发布文件夹
    release_dir = Path("release")
    if release_dir.exists():
        shutil.rmtree(release_dir)
    release_dir.mkdir()
    
    # 复制主程序
    shutil.copy2(exe_path, release_dir)
    print("✅ 已复制主程序")
    
    # 复制文档和配置文件
    files_to_copy = [
        "使用说明_GUI版.md",
        "README.md", 
        "config_example.py",
        "accounts_example.txt",
        "requirements.txt"
    ]
    
    for file_name in files_to_copy:
        if os.path.exists(file_name):
            shutil.copy2(file_name, release_dir)
            print(f"✅ 已复制 {file_name}")
    
    # 创建启动脚本
    start_script = release_dir / "启动程序.bat"
    with open(start_script, "w", encoding="utf-8") as f:
        f.write("@echo off\n")
        f.write("chcp 65001 >nul\n")
        f.write("echo 正在启动智慧中小学自动登录工具...\n")
        f.write('"智慧中小学自动登录工具.exe"\n')
    
    print("✅ 已创建启动脚本")
    
    return True

def main():
    """主函数"""
    print_banner()
    
    try:
        # 检查依赖
        check_dependencies()
        
        # 清理构建文件
        clean_build()
        
        # 创建spec文件
        create_spec_file()
        
        # 构建exe
        if not build_exe():
            return
        
        # 创建发布包
        if create_release():
            print("\n" + "=" * 60)
            print("🎉 打包完成！")
            print("📁 发布文件夹：release/")
            print("🚀 主程序：release/智慧中小学自动登录工具.exe")
            print("=" * 60)
        else:
            print("\n❌ 创建发布包失败")
            
    except Exception as e:
        print(f"\n❌ 打包过程中出现错误：{str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
