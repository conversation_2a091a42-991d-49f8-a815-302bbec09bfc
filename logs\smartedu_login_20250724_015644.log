2025-07-24 01:56:44,915 - INFO - ==================================================
2025-07-24 01:56:44,915 - INFO - 智慧中小学自动登录脚本启动
2025-07-24 01:56:44,916 - INFO - 配置信息 - 用户名: 15629266999
2025-07-24 01:56:44,916 - INFO - 配置信息 - 登录URL: https://auth.smartedu.cn/uias/login
2025-07-24 01:56:44,916 - INFO - 配置信息 - 目标URL: https://basic.smartedu.cn/training/2025sqpx
2025-07-24 01:56:44,916 - INFO - ==================================================
2025-07-24 01:56:44,916 - INFO - 开始执行自动登录流程...
2025-07-24 01:56:44,916 - INFO - 正在初始化Chrome浏览器...
2025-07-24 01:56:48,047 - INFO - Chrome浏览器初始化成功
2025-07-24 01:56:48,048 - INFO - 正在访问登录页面: https://auth.smartedu.cn/uias/login
2025-07-24 01:56:48,950 - INFO - 登录页面加载成功
2025-07-24 01:56:50,055 - INFO - 开始填写登录表单...
2025-07-24 01:56:51,827 - INFO - 用户名输入完成
2025-07-24 01:56:54,265 - INFO - 密码输入完成
2025-07-24 01:56:55,561 - INFO - 已勾选同意协议
2025-07-24 01:56:56,442 - INFO - 点击登录按钮...
2025-07-24 01:56:57,271 - INFO - 登录按钮已点击
2025-07-24 01:56:57,272 - INFO - 等待验证码加载...
2025-07-24 01:57:00,289 - INFO - 检测到腾讯验证码元素: #tcaptcha_iframe_dy
2025-07-24 01:57:00,290 - INFO - 验证码已加载
2025-07-24 01:57:00,290 - INFO - 验证码处理尝试 1/3
2025-07-24 01:57:00,290 - INFO - 正在获取腾讯验证码图片...
2025-07-24 01:57:00,290 - INFO - 等待腾讯验证码iframe加载...
2025-07-24 01:57:05,306 - INFO - 找到验证码iframe，使用选择器: #tcaptcha_iframe_dy
2025-07-24 01:57:05,339 - INFO - 已切换到验证码iframe
2025-07-24 01:57:08,362 - INFO - 在iframe中找到 3 个带背景图的div元素
2025-07-24 01:57:08,378 - INFO - 从tc-bg-img获取到背景图片
2025-07-24 01:57:08,379 - INFO - 使用背景图片作为滑块图片
2025-07-24 01:57:08,382 - INFO - 背景图片URL: https://turing.captcha.qcloud.com/cap_union_new_getcapbysig?img_index=1&image=026109000020732b000000
2025-07-24 01:57:08,382 - INFO - 滑块图片URL: https://turing.captcha.qcloud.com/cap_union_new_getcapbysig?img_index=1&image=026109000020732b000000
2025-07-24 01:57:08,726 - INFO - 图片下载并编码成功，大小: 35329 bytes
2025-07-24 01:57:09,090 - INFO - 图片下载并编码成功，大小: 35329 bytes
2025-07-24 01:57:09,091 - INFO - 正在调用第三方API识别验证码...
2025-07-24 01:57:09,949 - INFO - 验证码识别API响应: {'msg': '识别成功', 'code': 10000, 'data': {'code': 0, 'data': '1', 'time': 0.3206970691680908, 'unique_code': 'b9e999ddbe8676ff67d6b51e7863ffe9'}}
2025-07-24 01:57:09,950 - INFO - 验证码识别成功，X坐标: 1
2025-07-24 01:57:09,950 - INFO - 开始执行腾讯验证码滑块拖拽，目标位置: 1
2025-07-24 01:57:09,970 - INFO - 切换到验证码iframe查找滑块
2025-07-24 01:57:09,978 - INFO - 在iframe中找到滑块元素: .tc-slider-normal
2025-07-24 01:57:09,993 - INFO - 滑块当前位置: 28.75, 宽度: 54
2025-07-24 01:57:09,993 - INFO - 调整后的移动距离: 50
2025-07-24 01:57:09,993 - INFO - 执行真实鼠标拖拽...
2025-07-24 01:57:16,496 - INFO - 真实鼠标拖拽操作完成，等待结果...
2025-07-24 01:57:18,718 - WARNING - 拖拽可能失败，移动距离: 0.0 像素
2025-07-24 01:57:18,719 - INFO - 滑块拖拽操作完成
2025-07-24 01:57:21,723 - INFO - 检查登录状态...
2025-07-24 01:57:24,738 - INFO - 当前页面URL: https://auth.smartedu.cn/uias/login
2025-07-24 01:57:24,759 - WARNING - 仍在登录页面，登录可能失败
2025-07-24 01:57:24,760 - WARNING - 验证码验证失败，2秒后重试...
2025-07-24 01:57:30,188 - INFO - 开始填写登录表单...
2025-07-24 01:57:31,858 - INFO - 用户名输入完成
2025-07-24 01:57:32,686 - INFO - 正在关闭浏览器...
2025-07-24 01:57:32,693 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))': /session/2d7ee46d52fb96824cddbc43480474a7
2025-07-24 01:57:36,747 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002B413507560>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /session/2d7ee46d52fb96824cddbc43480474a7
2025-07-24 01:57:40,818 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002B4135072C0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /session/2d7ee46d52fb96824cddbc43480474a7
2025-07-24 01:57:49,043 - INFO - 浏览器已关闭
2025-07-24 01:57:49,044 - INFO - 正在关闭浏览器...
2025-07-24 01:57:53,139 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002B4135502C0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /session/2d7ee46d52fb96824cddbc43480474a7
2025-07-24 01:57:57,223 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002B413550590>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /session/2d7ee46d52fb96824cddbc43480474a7
2025-07-24 01:58:01,303 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002B413505280>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /session/2d7ee46d52fb96824cddbc43480474a7
2025-07-24 01:58:09,433 - INFO - 浏览器已关闭
