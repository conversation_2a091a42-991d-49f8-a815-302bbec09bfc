#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智慧中小学自动登录 - GUI版本
现代化界面，支持批量处理、倒计时、日志显示等功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import customtkinter as ctk
import threading
import time
import json
import os
from datetime import datetime
import queue
import logging
from typing import List, Dict, Any

# 导入登录核心模块
from gui_login_core import GUISmartEduLogin, CallbackManager

# 设置CustomTkinter主题
ctk.set_appearance_mode("light")  # 可选: "light", "dark", "system"
ctk.set_default_color_theme("blue")  # 可选: "blue", "green", "dark-blue"

class SmartEduGUI:
    def __init__(self):
        self.root = ctk.CTk()
        self.root.title("智慧中小学自动登录工具 v2.0")
        self.root.geometry("1200x800")
        self.root.minsize(1000, 600)
        
        # 数据存储
        self.accounts = []
        self.current_task = None
        self.current_login_instance = None
        self.is_running = False
        self.is_paused = False
        self.countdown_time = 10
        self.countdown_active = False
        
        # 线程通信
        self.log_queue = queue.Queue()
        self.status_queue = queue.Queue()
        
        # 初始化界面
        self.setup_ui()
        self.load_accounts()
        
        # 启动日志处理
        self.root.after(100, self.process_queues)
        
    def setup_ui(self):
        """设置用户界面"""
        # 主容器
        self.main_frame = ctk.CTkFrame(self.root)
        self.main_frame.pack(fill="both", expand=True, padx=10, pady=10)
        
        # 创建左右分栏
        self.left_frame = ctk.CTkFrame(self.main_frame)
        self.left_frame.pack(side="left", fill="both", expand=True, padx=(0, 5))
        
        self.right_frame = ctk.CTkFrame(self.main_frame)
        self.right_frame.pack(side="right", fill="y", padx=(5, 0))
        
        # 设置左侧面板
        self.setup_left_panel()
        
        # 设置右侧面板
        self.setup_right_panel()
        
    def setup_left_panel(self):
        """设置左侧面板 - 账号管理和日志"""
        # 账号管理区域
        self.account_frame = ctk.CTkFrame(self.left_frame)
        self.account_frame.pack(fill="both", expand=True, padx=10, pady=(10, 5))
        
        # 账号管理标题
        title_label = ctk.CTkLabel(
            self.account_frame, 
            text="📋 账号管理", 
            font=ctk.CTkFont(size=16, weight="bold")
        )
        title_label.pack(pady=(10, 5))
        
        # 账号操作按钮区域
        button_frame = ctk.CTkFrame(self.account_frame)
        button_frame.pack(fill="x", padx=10, pady=5)
        
        # 账号操作按钮
        self.add_btn = ctk.CTkButton(
            button_frame, 
            text="➕ 添加账号", 
            command=self.add_account,
            width=100
        )
        self.add_btn.pack(side="left", padx=5)
        
        self.edit_btn = ctk.CTkButton(
            button_frame, 
            text="✏️ 编辑", 
            command=self.edit_account,
            width=80
        )
        self.edit_btn.pack(side="left", padx=5)
        
        self.delete_btn = ctk.CTkButton(
            button_frame, 
            text="🗑️ 删除", 
            command=self.delete_account,
            width=80,
            fg_color="red",
            hover_color="darkred"
        )
        self.delete_btn.pack(side="left", padx=5)
        
        self.import_btn = ctk.CTkButton(
            button_frame, 
            text="📁 导入", 
            command=self.import_accounts,
            width=80
        )
        self.import_btn.pack(side="left", padx=5)
        
        # 账号列表
        self.setup_account_list()
        
        # 日志区域
        self.log_frame = ctk.CTkFrame(self.left_frame)
        self.log_frame.pack(fill="both", expand=True, padx=10, pady=(5, 10))
        
        log_title = ctk.CTkLabel(
            self.log_frame, 
            text="📄 运行日志", 
            font=ctk.CTkFont(size=16, weight="bold")
        )
        log_title.pack(pady=(10, 5))
        
        # 日志文本框
        self.log_text = ctk.CTkTextbox(
            self.log_frame,
            height=200,
            font=ctk.CTkFont(family="Consolas", size=12)
        )
        self.log_text.pack(fill="both", expand=True, padx=10, pady=(0, 10))
        
    def setup_account_list(self):
        """设置账号列表"""
        # 账号列表框架
        list_frame = ctk.CTkFrame(self.account_frame)
        list_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        # 创建Treeview用于显示账号列表
        columns = ("姓名", "账号", "状态")
        self.account_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=8)
        
        # 设置列标题
        for col in columns:
            self.account_tree.heading(col, text=col)
            self.account_tree.column(col, width=120, anchor="center")
        
        # 滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient="vertical", command=self.account_tree.yview)
        self.account_tree.configure(yscrollcommand=scrollbar.set)
        
        # 打包
        self.account_tree.pack(side="left", fill="both", expand=True, padx=(10, 0), pady=10)
        scrollbar.pack(side="right", fill="y", pady=10, padx=(0, 10))
        
    def setup_right_panel(self):
        """设置右侧面板 - 控制区域和倒计时"""
        self.right_frame.configure(width=300)
        
        # 控制区域
        control_frame = ctk.CTkFrame(self.right_frame)
        control_frame.pack(fill="x", padx=10, pady=(10, 5))
        
        control_title = ctk.CTkLabel(
            control_frame, 
            text="🎮 操作控制", 
            font=ctk.CTkFont(size=16, weight="bold")
        )
        control_title.pack(pady=(10, 10))
        
        # 主要操作按钮
        self.start_btn = ctk.CTkButton(
            control_frame,
            text="🚀 开始执行",
            command=self.start_execution,
            height=40,
            font=ctk.CTkFont(size=14, weight="bold"),
            fg_color="green",
            hover_color="darkgreen"
        )
        self.start_btn.pack(fill="x", padx=10, pady=5)
        
        self.pause_btn = ctk.CTkButton(
            control_frame,
            text="⏸️ 暂停",
            command=self.pause_execution,
            height=35,
            state="disabled"
        )
        self.pause_btn.pack(fill="x", padx=10, pady=5)
        
        self.stop_btn = ctk.CTkButton(
            control_frame,
            text="⏹️ 停止",
            command=self.stop_execution,
            height=35,
            fg_color="red",
            hover_color="darkred",
            state="disabled"
        )
        self.stop_btn.pack(fill="x", padx=10, pady=5)
        
        # 设置区域
        settings_frame = ctk.CTkFrame(self.right_frame)
        settings_frame.pack(fill="x", padx=10, pady=5)
        
        settings_title = ctk.CTkLabel(
            settings_frame, 
            text="⚙️ 设置", 
            font=ctk.CTkFont(size=16, weight="bold")
        )
        settings_title.pack(pady=(10, 10))
        
        # 倒计时设置
        countdown_label = ctk.CTkLabel(settings_frame, text="验证码倒计时(秒):")
        countdown_label.pack(pady=(5, 0))
        
        self.countdown_var = ctk.StringVar(value="10")
        self.countdown_entry = ctk.CTkEntry(
            settings_frame,
            textvariable=self.countdown_var,
            width=100,
            justify="center"
        )
        self.countdown_entry.pack(pady=5)
        
        # 倒计时显示区域
        self.countdown_frame = ctk.CTkFrame(self.right_frame)
        self.countdown_frame.pack(fill="x", padx=10, pady=5)
        
        countdown_title = ctk.CTkLabel(
            self.countdown_frame, 
            text="⏰ 验证码倒计时", 
            font=ctk.CTkFont(size=16, weight="bold")
        )
        countdown_title.pack(pady=(10, 5))
        
        self.countdown_display = ctk.CTkLabel(
            self.countdown_frame,
            text="等待中...",
            font=ctk.CTkFont(size=24, weight="bold"),
            text_color="gray"
        )
        self.countdown_display.pack(pady=10)
        
        self.countdown_progress = ctk.CTkProgressBar(self.countdown_frame)
        self.countdown_progress.pack(fill="x", padx=10, pady=(0, 10))
        self.countdown_progress.set(0)
        
        # 状态显示区域
        status_frame = ctk.CTkFrame(self.right_frame)
        status_frame.pack(fill="both", expand=True, padx=10, pady=(5, 10))
        
        status_title = ctk.CTkLabel(
            status_frame, 
            text="📊 执行状态", 
            font=ctk.CTkFont(size=16, weight="bold")
        )
        status_title.pack(pady=(10, 10))
        
        self.status_label = ctk.CTkLabel(
            status_frame,
            text="就绪",
            font=ctk.CTkFont(size=14),
            wraplength=250
        )
        self.status_label.pack(pady=5)
        
        self.progress_label = ctk.CTkLabel(
            status_frame,
            text="进度: 0/0",
            font=ctk.CTkFont(size=12)
        )
        self.progress_label.pack(pady=5)
        
        self.progress_bar = ctk.CTkProgressBar(status_frame)
        self.progress_bar.pack(fill="x", padx=10, pady=10)
        self.progress_bar.set(0)

    def add_account(self):
        """添加账号对话框"""
        dialog = AccountDialog(self.root, "添加账号")
        if dialog.result:
            account = dialog.result
            self.accounts.append(account)
            self.refresh_account_list()
            self.save_accounts()
            self.log_message(f"✅ 已添加账号: {account['name']}")

    def edit_account(self):
        """编辑选中的账号"""
        selection = self.account_tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请先选择要编辑的账号")
            return

        item = selection[0]
        index = self.account_tree.index(item)
        account = self.accounts[index]

        dialog = AccountDialog(self.root, "编辑账号", account)
        if dialog.result:
            self.accounts[index] = dialog.result
            self.refresh_account_list()
            self.save_accounts()
            self.log_message(f"✅ 已更新账号: {dialog.result['name']}")

    def delete_account(self):
        """删除选中的账号"""
        selection = self.account_tree.selection()
        if not selection:
            messagebox.showwarning("提示", "请先选择要删除的账号")
            return

        if messagebox.askyesno("确认删除", "确定要删除选中的账号吗？"):
            item = selection[0]
            index = self.account_tree.index(item)
            account_name = self.accounts[index]['name']
            del self.accounts[index]
            self.refresh_account_list()
            self.save_accounts()
            self.log_message(f"🗑️ 已删除账号: {account_name}")

    def import_accounts(self):
        """从txt文件导入账号"""
        file_path = filedialog.askopenfilename(
            title="选择账号文件",
            filetypes=[("文本文件", "*.txt"), ("所有文件", "*.*")]
        )

        if file_path:
            try:
                imported_count = 0
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()

                for line_num, line in enumerate(lines, 1):
                    line = line.strip()
                    if not line or line.startswith('#'):
                        continue

                    parts = line.split()
                    if len(parts) >= 3:
                        name = parts[0]
                        username = parts[1]
                        password = ' '.join(parts[2:])

                        account = {
                            "name": name,
                            "username": username,
                            "password": password,
                            "enabled": True
                        }
                        self.accounts.append(account)
                        imported_count += 1

                self.refresh_account_list()
                self.save_accounts()
                self.log_message(f"📁 成功导入 {imported_count} 个账号")
                messagebox.showinfo("导入成功", f"成功导入 {imported_count} 个账号")

            except Exception as e:
                self.log_message(f"❌ 导入失败: {str(e)}")
                messagebox.showerror("导入失败", f"导入账号时发生错误:\n{str(e)}")

    def refresh_account_list(self):
        """刷新账号列表显示"""
        # 清空现有项目
        for item in self.account_tree.get_children():
            self.account_tree.delete(item)

        # 添加账号到列表
        for account in self.accounts:
            status = "✅ 启用" if account.get("enabled", True) else "❌ 禁用"
            self.account_tree.insert("", "end", values=(
                account["name"],
                account["username"],
                status
            ))

    def load_accounts(self):
        """从文件加载账号"""
        try:
            if os.path.exists("accounts.json"):
                with open("accounts.json", 'r', encoding='utf-8') as f:
                    self.accounts = json.load(f)
                self.refresh_account_list()
                self.log_message(f"📋 已加载 {len(self.accounts)} 个账号")
        except Exception as e:
            self.log_message(f"⚠️ 加载账号失败: {str(e)}")

    def save_accounts(self):
        """保存账号到文件"""
        try:
            with open("accounts.json", 'w', encoding='utf-8') as f:
                json.dump(self.accounts, f, ensure_ascii=False, indent=2)
        except Exception as e:
            self.log_message(f"⚠️ 保存账号失败: {str(e)}")

    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        # 添加到队列，由主线程处理
        self.log_queue.put(log_entry)

    def process_queues(self):
        """处理队列中的消息"""
        # 处理日志队列
        try:
            while True:
                log_entry = self.log_queue.get_nowait()
                self.log_text.insert("end", log_entry)
                self.log_text.see("end")
        except queue.Empty:
            pass

        # 处理状态队列
        try:
            while True:
                status_data = self.status_queue.get_nowait()
                self.update_status(status_data)
        except queue.Empty:
            pass

        # 继续处理
        self.root.after(100, self.process_queues)

    def update_status(self, status_data):
        """更新状态显示"""
        if "message" in status_data:
            self.status_label.configure(text=status_data["message"])

        if "progress" in status_data:
            self.progress_bar.set(status_data["progress"])

        if "progress_text" in status_data:
            self.progress_label.configure(text=status_data["progress_text"])

    def start_execution(self):
        """开始执行任务"""
        if not self.accounts:
            messagebox.showwarning("提示", "请先添加账号")
            return

        enabled_accounts = [acc for acc in self.accounts if acc.get("enabled", True)]
        if not enabled_accounts:
            messagebox.showwarning("提示", "没有启用的账号")
            return

        self.is_running = True
        self.is_paused = False

        # 更新按钮状态
        self.start_btn.configure(state="disabled")
        self.pause_btn.configure(state="normal")
        self.stop_btn.configure(state="normal")

        # 获取倒计时设置
        try:
            self.countdown_time = int(self.countdown_var.get())
        except ValueError:
            self.countdown_time = 10
            self.countdown_var.set("10")

        self.log_message("🚀 开始执行批量登录任务")

        # 在新线程中执行任务
        self.current_task = threading.Thread(target=self.execute_batch_login, args=(enabled_accounts,))
        self.current_task.daemon = True
        self.current_task.start()

    def pause_execution(self):
        """暂停/继续执行"""
        if self.is_paused:
            self.is_paused = False
            self.pause_btn.configure(text="⏸️ 暂停")
            self.log_message("▶️ 继续执行")
        else:
            self.is_paused = True
            self.pause_btn.configure(text="▶️ 继续")
            self.log_message("⏸️ 已暂停执行")



    def execute_batch_login(self, accounts):
        """执行批量登录（在后台线程中运行）"""
        total_accounts = len(accounts)

        for index, account in enumerate(accounts):
            if not self.is_running:
                break

            # 等待暂停状态结束
            while self.is_paused and self.is_running:
                time.sleep(0.5)

            if not self.is_running:
                break

            # 更新状态
            self.status_queue.put({
                "message": f"正在处理: {account['name']}",
                "progress": index / total_accounts,
                "progress_text": f"进度: {index + 1}/{total_accounts}"
            })

            self.log_message(f"🔄 开始处理账号: {account['name']}")

            try:
                # 这里调用登录逻辑
                success = self.process_single_account(account)

                if success:
                    self.log_message(f"✅ 账号 {account['name']} 处理成功")
                else:
                    self.log_message(f"❌ 账号 {account['name']} 处理失败")

            except Exception as e:
                self.log_message(f"💥 账号 {account['name']} 处理异常: {str(e)}")

            # 账号间延迟
            if index < total_accounts - 1 and self.is_running:
                self.log_message("⏰ 等待5秒后处理下一个账号...")
                for _ in range(50):  # 5秒，每0.1秒检查一次
                    if not self.is_running:
                        break
                    time.sleep(0.1)

        # 任务完成
        if self.is_running:
            self.status_queue.put({
                "message": "所有任务已完成",
                "progress": 1.0,
                "progress_text": f"进度: {total_accounts}/{total_accounts}"
            })
            self.log_message("🎉 所有账号处理完成！")

        # 重置状态
        self.is_running = False
        self.root.after(0, self.stop_execution)  # 在主线程中更新UI

    def process_single_account(self, account):
        """处理单个账号（真实登录过程）"""
        try:
            # 创建回调管理器
            callback_manager = CallbackManager(self)

            # 创建登录实例
            login_instance = GUISmartEduLogin(account, callback_manager)
            login_instance.set_countdown_time(self.countdown_time)

            # 存储当前登录实例，用于停止控制
            self.current_login_instance = login_instance

            # 执行登录
            success = login_instance.run()

            return success

        except Exception as e:
            self.log_message(f"  ❌ 处理失败: {str(e)}")
            return False

    def start_countdown(self):
        """开始验证码倒计时"""
        self.countdown_active = True
        self.log_message(f"⏰ 开始验证码倒计时 ({self.countdown_time}秒)")

        # 在新线程中运行倒计时
        countdown_thread = threading.Thread(target=self.run_countdown)
        countdown_thread.daemon = True
        countdown_thread.start()

    def run_countdown(self):
        """运行倒计时（在后台线程中）"""
        remaining = self.countdown_time

        while remaining > 0 and self.countdown_active and self.is_running:
            # 更新倒计时显示
            self.root.after(0, self.update_countdown_display, remaining)

            time.sleep(1)
            remaining -= 1

        # 倒计时结束
        self.countdown_active = False
        self.root.after(0, self.countdown_finished)

    def update_countdown_display(self, remaining):
        """更新倒计时显示（在主线程中调用）"""
        self.countdown_display.configure(
            text=f"{remaining}",
            text_color="red" if remaining <= 3 else "orange" if remaining <= 5 else "blue"
        )

        progress = 1 - (remaining / self.countdown_time)
        self.countdown_progress.set(progress)

    def countdown_finished(self):
        """倒计时结束"""
        self.countdown_display.configure(text="完成", text_color="green")
        self.countdown_progress.set(1)
        self.log_message("✅ 验证码倒计时结束")

    def start_countdown_from_core(self, seconds):
        """从核心模块触发倒计时"""
        self.countdown_time = seconds
        self.start_countdown()

    def stop_execution(self):
        """停止执行"""
        self.is_running = False
        self.is_paused = False

        # 停止当前登录实例
        if self.current_login_instance:
            self.current_login_instance.stop()

        # 更新按钮状态
        self.start_btn.configure(state="normal")
        self.pause_btn.configure(state="disabled", text="⏸️ 暂停")
        self.stop_btn.configure(state="disabled")

        # 重置倒计时显示
        self.countdown_display.configure(text="等待中...", text_color="gray")
        self.countdown_progress.set(0)

        self.log_message("⏹️ 已停止执行")
        self.status_queue.put({"message": "已停止", "progress": 0, "progress_text": "进度: 0/0"})

    def run(self):
        """运行GUI应用"""
        self.root.mainloop()


class AccountDialog:
    """账号添加/编辑对话框"""
    def __init__(self, parent, title, account=None):
        self.result = None

        # 创建对话框窗口
        self.dialog = ctk.CTkToplevel(parent)
        self.dialog.title(title)
        self.dialog.geometry("400x300")
        self.dialog.resizable(False, False)

        # 设置为模态对话框
        self.dialog.transient(parent)
        self.dialog.grab_set()

        # 居中显示
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() // 2) - (400 // 2)
        y = (self.dialog.winfo_screenheight() // 2) - (300 // 2)
        self.dialog.geometry(f"400x300+{x}+{y}")

        # 创建界面
        self.setup_dialog(account)

        # 等待对话框关闭
        self.dialog.wait_window()

    def setup_dialog(self, account):
        """设置对话框界面"""
        main_frame = ctk.CTkFrame(self.dialog)
        main_frame.pack(fill="both", expand=True, padx=20, pady=20)

        # 标题
        title_label = ctk.CTkLabel(
            main_frame,
            text="账号信息",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        title_label.pack(pady=(10, 20))

        # 姓名输入
        name_label = ctk.CTkLabel(main_frame, text="姓名:")
        name_label.pack(anchor="w", padx=20)

        self.name_var = ctk.StringVar(value=account["name"] if account else "")
        self.name_entry = ctk.CTkEntry(main_frame, textvariable=self.name_var, width=300)
        self.name_entry.pack(pady=(5, 15), padx=20)

        # 账号输入
        username_label = ctk.CTkLabel(main_frame, text="账号:")
        username_label.pack(anchor="w", padx=20)

        self.username_var = ctk.StringVar(value=account["username"] if account else "")
        self.username_entry = ctk.CTkEntry(main_frame, textvariable=self.username_var, width=300)
        self.username_entry.pack(pady=(5, 15), padx=20)

        # 密码输入
        password_label = ctk.CTkLabel(main_frame, text="密码:")
        password_label.pack(anchor="w", padx=20)

        self.password_var = ctk.StringVar(value=account["password"] if account else "")
        self.password_entry = ctk.CTkEntry(main_frame, textvariable=self.password_var, width=300, show="*")
        self.password_entry.pack(pady=(5, 15), padx=20)

        # 启用状态
        self.enabled_var = ctk.BooleanVar(value=account.get("enabled", True) if account else True)
        self.enabled_checkbox = ctk.CTkCheckBox(main_frame, text="启用此账号", variable=self.enabled_var)
        self.enabled_checkbox.pack(pady=10)

        # 按钮区域
        button_frame = ctk.CTkFrame(main_frame)
        button_frame.pack(fill="x", pady=(20, 10), padx=20)

        # 确定按钮
        ok_button = ctk.CTkButton(
            button_frame,
            text="确定",
            command=self.ok_clicked,
            width=100
        )
        ok_button.pack(side="right", padx=(10, 0))

        # 取消按钮
        cancel_button = ctk.CTkButton(
            button_frame,
            text="取消",
            command=self.cancel_clicked,
            width=100,
            fg_color="gray",
            hover_color="darkgray"
        )
        cancel_button.pack(side="right")

        # 设置焦点
        self.name_entry.focus()

    def ok_clicked(self):
        """确定按钮点击"""
        name = self.name_var.get().strip()
        username = self.username_var.get().strip()
        password = self.password_var.get().strip()

        if not name:
            messagebox.showwarning("提示", "请输入姓名")
            return

        if not username:
            messagebox.showwarning("提示", "请输入账号")
            return

        if not password:
            messagebox.showwarning("提示", "请输入密码")
            return

        self.result = {
            "name": name,
            "username": username,
            "password": password,
            "enabled": self.enabled_var.get()
        }

        self.dialog.destroy()

    def cancel_clicked(self):
        """取消按钮点击"""
        self.dialog.destroy()


def main():
    """主函数"""
    app = SmartEduGUI()
    app.run()


if __name__ == "__main__":
    main()
