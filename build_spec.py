#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyInstaller打包配置文件生成器
"""

import os

# 创建.spec文件内容
spec_content = '''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['gui_main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('requirements.txt', '.'),
        ('使用说明_GUI版.md', '.'),
    ],
    hiddenimports=[
        'customtkinter',
        'selenium',
        'PIL',
        'selenium.webdriver.chrome.service',
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.filedialog',
        'threading',
        'queue',
        'json',
        'datetime',
        'logging',
        'subprocess',
        'random',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='智慧中小学自动登录工具',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    version='version_info.txt',
    icon='icon.ico' if os.path.exists('icon.ico') else None,
)
'''

# 写入.spec文件
with open('gui_app.spec', 'w', encoding='utf-8') as f:
    f.write(spec_content)

print("✅ PyInstaller配置文件已生成: gui_app.spec")
print("使用命令: pyinstaller gui_app.spec")
