# -*- coding: utf-8 -*-
"""
智慧中小学自动登录配置文件示例
请复制此文件为 config.py 并修改相关配置信息
"""

# 批量登录账号配置 - 支持多个账号
ACCOUNTS_CONFIG = [
    {
        "username": "账号1的用户名",
        "password": "账号1的密码",
        "name": "张三的账号",  # 账号备注名称，用于日志和截图文件命名
        "enabled": True       # 是否启用此账号，设置为False可以跳过
    },
    {
        "username": "账号2的用户名",
        "password": "账号2的密码", 
        "name": "李四的账号",
        "enabled": True
    },
    {
        "username": "账号3的用户名",
        "password": "账号3的密码",
        "name": "王五的账号",
        "enabled": False  # 此账号将被跳过
    },
    # 可以继续添加更多账号...
    # {
    #     "username": "更多账号的用户名",
    #     "password": "更多账号的密码",
    #     "name": "更多账号备注",
    #     "enabled": True
    # },
]

# 登录页面配置
LOGIN_CONFIG = {
    "login_url": "https://auth.smartedu.cn/uias/login",
    "target_url": "https://basic.smartedu.cn/training/2025sqpx"
}

# 验证码配置 - 手动操作模式
CAPTCHA_CONFIG = {
    "manual_mode": True,        # 启用手动模式（不使用第三方识别）
    "wait_time": 60,           # 手动操作等待时间（秒）
    "show_tips": True          # 是否显示操作提示
}

# 浏览器配置
BROWSER_CONFIG = {
    "window_size": "1920,1080",
    "headless": False,  # 批量处理时建议设置为False以便手动操作验证码
    "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
}

# 重试配置
RETRY_CONFIG = {
    "max_retries": 2,   # 减少重试次数，因为需要手动操作
    "retry_delay": 3,   # 秒
    "timeout": 30       # 增加页面加载超时时间
}

# 批量处理配置
BATCH_CONFIG = {
    "account_delay": 5,         # 账号之间的间隔时间（秒）
    "continue_on_error": True,  # 某个账号失败时是否继续处理下一个
    "max_concurrent": 1,        # 最大并发数（建议为1，避免被检测）
    "save_individual_logs": True  # 是否为每个账号保存单独的日志
}

# 日志配置
LOG_CONFIG = {
    "level": "INFO",  # DEBUG, INFO, WARNING, ERROR
    "save_to_file": True,
    "log_dir": "logs"
}

# 截图配置
SCREENSHOT_CONFIG = {
    "save_dir": "screenshots",
    "crop_ratio": 0.8,  # 截取上半部分的比例
    "format": "PNG",
    "include_account_name": True  # 截图文件名是否包含账号名称
}
