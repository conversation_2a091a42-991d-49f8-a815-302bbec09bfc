#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智慧中小学自动登录脚本
功能：自动登录智慧中小学平台，处理滑块验证码，并截图指定页面
作者：AI Assistant
日期：2025-07-23
"""

import time
import random
import logging
import os
import sys
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException
from PIL import Image
import io

# 导入配置文件
try:
    from config import ACCOUNTS_CONFIG, LOGIN_CONFIG, CAPTCHA_CONFIG, BROWSER_CONFIG, RETRY_CONFIG, LOG_CONFIG, SCREENSHOT_CONFIG, BATCH_CONFIG
except ImportError:
    # 如果配置文件不存在，使用默认配置
    ACCOUNTS_CONFIG = [
        {
            "username": "默认用户名",
            "password": "默认密码",
            "name": "默认账号",
            "enabled": True
        }
    ]
    LOGIN_CONFIG = {
        "login_url": "https://auth.smartedu.cn/uias/login",
        "target_url": "https://basic.smartedu.cn/training/2025sqpx"
    }
    CAPTCHA_CONFIG = {
        "manual_mode": True,
        "wait_time": 60,
        "show_tips": True
    }
    BROWSER_CONFIG = {
        "window_size": "1920,1080",
        "headless": False,
        "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
    }
    RETRY_CONFIG = {
        "max_retries": 2,
        "retry_delay": 3,
        "timeout": 30
    }
    BATCH_CONFIG = {
        "account_delay": 5,
        "continue_on_error": True,
        "max_concurrent": 1,
        "save_individual_logs": True
    }
    LOG_CONFIG = {
        "level": "INFO",
        "save_to_file": True,
        "log_dir": "logs",
        "console_format": "simple"  # 控制台使用简单格式
    }
    SCREENSHOT_CONFIG = {
        "save_dir": "screenshots",
        "crop_ratio": 0.8,
        "format": "PNG",
        "include_account_name": True
    }

class SmartEduAutoLogin:
    def __init__(self, account_info=None, account_index=0):
        """初始化自动登录类"""
        # 从配置文件加载配置信息
        self.LOGIN_URL = LOGIN_CONFIG["login_url"]
        self.TARGET_URL = LOGIN_CONFIG["target_url"]

        # 账号信息
        if account_info:
            self.USERNAME = account_info["username"]
            self.PASSWORD = account_info["password"]
            self.ACCOUNT_NAME = account_info.get("name", f"账号{account_index + 1}")
        else:
            # 兼容旧版本，使用第一个账号
            if ACCOUNTS_CONFIG:
                self.USERNAME = ACCOUNTS_CONFIG[0]["username"]
                self.PASSWORD = ACCOUNTS_CONFIG[0]["password"]
                self.ACCOUNT_NAME = ACCOUNTS_CONFIG[0].get("name", "默认账号")
            else:
                raise Exception("未找到有效的账号配置")

        # 验证码配置（手动模式）
        self.MANUAL_CAPTCHA = CAPTCHA_CONFIG.get("manual_mode", True)
        self.CAPTCHA_WAIT_TIME = CAPTCHA_CONFIG.get("wait_time", 60)
        self.SHOW_CAPTCHA_TIPS = CAPTCHA_CONFIG.get("show_tips", True)

        # 浏览器配置
        self.driver = None
        self.wait = None
        self.account_index = account_index

        # 重试配置
        self.MAX_RETRIES = RETRY_CONFIG["max_retries"]
        self.RETRY_DELAY = RETRY_CONFIG["retry_delay"]
        self.TIMEOUT = RETRY_CONFIG["timeout"]

        # 设置日志
        self.setup_logging()
        
    def setup_logging(self):
        """设置详细的日志输出"""
        # 为每个账号创建单独的日志文件
        if BATCH_CONFIG.get("save_individual_logs", True):
            log_filename = f"smartedu_login_{self.ACCOUNT_NAME}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        else:
            log_filename = f"smartedu_login_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"

        # 创建日志目录
        log_dir = LOG_CONFIG.get("log_dir", "logs")
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)

        # 设置日志级别为INFO
        log_level = logging.INFO

        # 为每个账号创建独立的logger
        logger_name = f"{__name__}_{self.account_index}"
        self.logger = logging.getLogger(logger_name)

        # 清除已有的处理器，避免重复日志
        self.logger.handlers.clear()

        # 设置日志级别
        self.logger.setLevel(log_level)

        # 创建格式器 - 只显示消息内容，不显示时间戳和级别
        formatter = logging.Formatter('%(message)s')

        # 控制台处理器 - 只输出INFO级别
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)

        # 添加过滤器，只允许INFO级别的日志通过
        class InfoOnlyFilter(logging.Filter):
            def filter(self, record):
                return record.levelno == logging.INFO

        console_handler.addFilter(InfoOnlyFilter())
        self.logger.addHandler(console_handler)

        # 文件处理器 - 保持完整格式用于文件记录
        if LOG_CONFIG.get("save_to_file", True):
            file_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
            file_handler = logging.FileHandler(f'{log_dir}/{log_filename}', encoding='utf-8')
            file_handler.setFormatter(file_formatter)
            self.logger.addHandler(file_handler)

        self.logger.info("=" * 60)
        self.logger.info(f"智慧中小学自动登录脚本启动 - {self.ACCOUNT_NAME}")
        self.logger.info(f"配置信息 - 账号名称: {self.ACCOUNT_NAME}")
        self.logger.info(f"配置信息 - 用户名: {self.USERNAME}")
        self.logger.info(f"配置信息 - 登录URL: {self.LOGIN_URL}")
        self.logger.info(f"配置信息 - 目标URL: {self.TARGET_URL}")
        self.logger.info(f"配置信息 - 验证码模式: {'手动操作' if self.MANUAL_CAPTCHA else '自动识别'}")
        self.logger.info("=" * 60)
    
    def get_chromedriver_path(self):
        """获取ChromeDriver路径，优先使用内置驱动"""
        try:
            # 获取程序运行目录
            if getattr(sys, 'frozen', False):
                # 如果是打包后的exe文件
                app_dir = os.path.dirname(sys.executable)
            else:
                # 如果是Python脚本
                app_dir = os.path.dirname(os.path.abspath(__file__))

            # 内置驱动路径（优先级最高）
            bundled_paths = [
                os.path.join(app_dir, "chromedriver.exe"),
                os.path.join(app_dir, "drivers", "chromedriver.exe"),
                os.path.join(app_dir, "bin", "chromedriver.exe"),
            ]

            # 检查内置驱动
            for path in bundled_paths:
                if os.path.exists(path):
                    self.logger.info(f"找到内置ChromeDriver: {path}")
                    return path

            # 如果没有内置驱动，尝试其他路径
            fallback_paths = [
                "chromedriver.exe",
                "./chromedriver.exe",
                "C:/chromedriver/chromedriver.exe",
            ]

            for path in fallback_paths:
                if os.path.exists(path):
                    self.logger.info(f"找到ChromeDriver: {path}")
                    return path

            return None

        except Exception as e:
            self.logger.error(f"获取ChromeDriver路径失败: {str(e)}")
            return None

    def setup_driver(self):
        """设置Chrome浏览器驱动，优先使用内置驱动"""
        try:
            self.logger.info("正在初始化Chrome浏览器...")

            chrome_options = Options()

            # 基本反检测设置 - 使用最兼容的配置
            chrome_options.add_argument('--disable-blink-features=AutomationControlled')
            chrome_options.add_experimental_option('useAutomationExtension', False)

            # 设置用户代理
            user_agent = BROWSER_CONFIG.get("user_agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
            chrome_options.add_argument(f'--user-agent={user_agent}')

            # 基本设置
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--disable-extensions')

            # 窗口大小
            window_size = BROWSER_CONFIG.get("window_size", "1920,1080")
            chrome_options.add_argument(f'--window-size={window_size}')

            # 无头模式
            if BROWSER_CONFIG.get("headless", False):
                chrome_options.add_argument('--headless')

            # 获取ChromeDriver路径
            chromedriver_path = self.get_chromedriver_path()

            if chromedriver_path:
                # 使用指定路径的ChromeDriver
                try:
                    from selenium.webdriver.chrome.service import Service
                    service = Service(chromedriver_path)
                    self.driver = webdriver.Chrome(service=service, options=chrome_options)
                    self.logger.info(f"使用指定ChromeDriver成功: {chromedriver_path}")
                except Exception as e:
                    self.logger.warning(f"使用指定ChromeDriver失败: {str(e)}")
                    raise
            else:
                # 尝试使用系统PATH中的chromedriver
                try:
                    self.driver = webdriver.Chrome(options=chrome_options)
                    self.logger.info("使用系统ChromeDriver成功")
                except Exception as e:
                    self.logger.error(f"使用系统ChromeDriver失败: {str(e)}")
                    # 最后尝试webdriver-manager（需要网络）
                    try:
                        from webdriver_manager.chrome import ChromeDriverManager
                        from selenium.webdriver.chrome.service import Service
                        service = Service(ChromeDriverManager().install())
                        self.driver = webdriver.Chrome(service=service, options=chrome_options)
                        self.logger.info("使用webdriver-manager成功")
                    except Exception as e2:
                        self.logger.error(f"webdriver-manager也失败: {str(e2)}")
                        raise Exception("无法找到可用的ChromeDriver。请确保：\n1. Chrome浏览器已安装\n2. 将chromedriver.exe放在程序目录下\n3. 或者确保网络连接正常以自动下载驱动")

            # 执行反检测脚本
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")

            self.wait = WebDriverWait(self.driver, self.TIMEOUT)
            self.logger.info("Chrome浏览器初始化成功")

        except Exception as e:
            self.logger.error(f"浏览器初始化失败: {str(e)}")
            raise
    
    def human_like_delay(self, min_delay=0.5, max_delay=2.0):
        """模拟人类操作的随机延迟"""
        delay = random.uniform(min_delay, max_delay)
        time.sleep(delay)
        return delay
    
    def navigate_to_login(self):
        """导航到登录页面"""
        try:
            self.logger.info(f"正在访问登录页面: {self.LOGIN_URL}")
            self.driver.get(self.LOGIN_URL)
            
            # 等待页面加载
            self.wait.until(EC.presence_of_element_located((By.ID, "username")))
            self.logger.info("登录页面加载成功")
            
            # 模拟人类浏览行为
            self.human_like_delay(1, 3)
            
        except TimeoutException:
            self.logger.error("登录页面加载超时")
            raise
        except Exception as e:
            self.logger.error(f"访问登录页面失败: {str(e)}")
            raise
    
    def fill_login_form(self):
        """填写登录表单（快速输入）"""
        try:
            self.logger.info("开始填写登录表单...")

            # 快速输入用户名
            username_input = self.wait.until(EC.element_to_be_clickable((By.ID, "username")))
            username_input.clear()
            username_input.send_keys(self.USERNAME)

            self.logger.info("用户名输入完成")
            self.human_like_delay(0.3, 0.8)

            # 快速输入密码
            password_input = self.driver.find_element(By.ID, "tmpPassword")
            password_input.clear()
            password_input.send_keys(self.PASSWORD)

            self.logger.info("密码输入完成")
            self.human_like_delay(0.3, 0.8)

            # 勾选同意协议
            agreement_checkbox = self.driver.find_element(By.ID, "agreementCheckbox")
            if not agreement_checkbox.is_selected():
                agreement_checkbox.click()
                self.logger.info("已勾选同意协议")

            self.human_like_delay(0.3, 0.6)

        except Exception as e:
            self.logger.error(f"填写登录表单失败: {str(e)}")
            raise
    
    def click_login_button(self):
        """点击登录按钮"""
        try:
            self.logger.info("点击登录按钮...")
            login_btn = self.wait.until(EC.element_to_be_clickable((By.ID, "loginBtn")))

            # 模拟人类点击
            ActionChains(self.driver).move_to_element(login_btn).pause(0.5).click().perform()
            self.logger.info("登录按钮已点击")

            # 等待验证码出现，增加等待时间
            self.logger.info("等待验证码加载...")
            time.sleep(3)

            # 检查是否出现了腾讯验证码
            captcha_appeared = False
            for _ in range(15):  # 最多等待15秒
                try:
                    # 检查腾讯验证码相关元素
                    captcha_selectors = [
                        "#tcaptcha_iframe_dy",
                        "#captcha-container",
                        "#uc-slider-captcha",
                        "[id*='tcaptcha']",
                        "[class*='captcha']",
                        "[class*='slider']"
                    ]

                    for selector in captcha_selectors:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        if elements:
                            self.logger.info(f"检测到腾讯验证码元素: {selector}")
                            captcha_appeared = True
                            break

                    if captcha_appeared:
                        break

                    time.sleep(1)

                except:
                    time.sleep(1)
                    continue

            if not captcha_appeared:
                self.logger.warning("未检测到验证码元素，可能页面结构发生变化")
                # 保存截图用于调试
                self.driver.save_screenshot("no_captcha_debug.png")
                self.logger.info("已保存调试截图: no_captcha_debug.png")
            else:
                self.logger.info("验证码已加载")

        except Exception as e:
            self.logger.error(f"点击登录按钮失败: {str(e)}")
            raise

    def handle_manual_captcha(self):
        """处理手动验证码操作 - 固定10秒倒计时版"""
        try:
            self.logger.info("=" * 50)
            self.logger.info("🔧 验证码需要手动操作")
            self.logger.info("=" * 50)

            if self.SHOW_CAPTCHA_TIPS:
                print(f"\n{'='*60}")
                print(f"📋 【{self.ACCOUNT_NAME}】验证码操作提示:")
                print(f"{'='*60}")
                print("🔍 请在浏览器中完成以下操作:")
                print("   1. 查看验证码图片")
                print("   2. 拖拽滑块到正确位置")
                print("   3. 等待验证通过")
                print(f"{'='*60}")
                print("⏰ 操作时间: 10 秒（固定倒计时）")
                print(f"{'='*60}\n")

            # 固定10秒倒计时
            countdown_time = 10
            self.logger.info(f"开始 {countdown_time} 秒倒计时，请完成验证码操作...")

            for remaining_time in range(countdown_time, 0, -1):
                print(f"⏰ 倒计时: {remaining_time} 秒", end="\r")
                self.logger.info(f"⏰ 倒计时: {remaining_time} 秒")
                time.sleep(1)

            print("\n⏰ 倒计时结束！正在检测登录状态...")
            self.logger.info("⏰ 倒计时结束！正在检测登录状态...")

            # 倒计时结束后检测登录状态
            time.sleep(2)  # 稍等片刻让页面响应
            
            if self.check_login_success():
                self.logger.info("✅ 检测到登录成功！")
                print("✅ 验证码验证成功，登录成功！")
                return True
            else:
                self.logger.warning("❌ 登录检测失败，可能验证码未完成或登录失败")
                print("❌ 登录检测失败，验证码可能未完成")
                return False

        except Exception as e:
            self.logger.error(f"手动验证码处理失败: {str(e)}")
            return False

    def wait_for_captcha_appear(self):
        """等待验证码出现"""
        try:
            self.logger.info("等待验证码加载...")
            time.sleep(3)

            # 检查是否出现了腾讯验证码
            captcha_appeared = False
            for _ in range(15):  # 最多等待15秒
                try:
                    # 检查腾讯验证码相关元素
                    captcha_selectors = [
                        "#tcaptcha_iframe_dy",
                        "#captcha-container",
                        "#uc-slider-captcha",
                        "[id*='tcaptcha']",
                        "[class*='captcha']",
                        "[class*='slider']"
                    ]

                    for selector in captcha_selectors:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        if elements:
                            self.logger.info(f"检测到验证码元素: {selector}")
                            captcha_appeared = True
                            break

                    if captcha_appeared:
                        break

                    time.sleep(1)

                except:
                    time.sleep(1)
                    continue

            if not captcha_appeared:
                self.logger.warning("未检测到验证码元素，可能页面结构发生变化")
                # 保存截图用于调试
                debug_screenshot = f"no_captcha_debug_{self.ACCOUNT_NAME}_{datetime.now().strftime('%H%M%S')}.png"
                self.driver.save_screenshot(debug_screenshot)
                self.logger.info(f"已保存调试截图: {debug_screenshot}")
                return False
            else:
                self.logger.info("验证码已加载，准备手动操作")
                return True

        except Exception as e:
            self.logger.error(f"等待验证码出现失败: {str(e)}")
            return False

    def handle_captcha_with_retry(self):
        """处理验证码，包含重试机制（纯手动模式）"""
        for attempt in range(self.MAX_RETRIES):
            try:
                self.logger.info(f"验证码处理尝试 {attempt + 1}/{self.MAX_RETRIES}")

                # 等待验证码出现
                if not self.wait_for_captcha_appear():
                    self.logger.warning("验证码未出现，可能已经登录成功或页面异常")
                    # 检查是否已经登录成功
                    if self.check_login_success():
                        self.logger.info("检测到已登录成功，无需处理验证码")
                        return True
                    else:
                        if attempt < self.MAX_RETRIES - 1:
                            self.logger.info(f"等待 {self.RETRY_DELAY} 秒后重试...")
                            time.sleep(self.RETRY_DELAY)
                            continue
                        else:
                            return False

                # 手动处理验证码
                if self.handle_manual_captcha():
                    # 检查登录是否成功
                    time.sleep(2)  # 等待页面响应
                    if self.check_login_success():
                        self.logger.info("验证码处理成功，登录成功")
                        return True
                    else:
                        self.logger.warning("验证码操作完成但登录验证失败")
                        if attempt < self.MAX_RETRIES - 1:
                            self.logger.info(f"等待 {self.RETRY_DELAY} 秒后重试...")
                            time.sleep(self.RETRY_DELAY)
                            # 刷新页面重新开始
                            self.driver.refresh()
                            time.sleep(3)
                            self.fill_login_form()
                            self.click_login_button()
                        continue
                else:
                    self.logger.warning("手动验证码操作失败或超时")
                    if attempt < self.MAX_RETRIES - 1:
                        self.logger.info(f"等待 {self.RETRY_DELAY} 秒后重试...")
                        time.sleep(self.RETRY_DELAY)
                        # 刷新页面重新开始
                        self.driver.refresh()
                        time.sleep(3)
                        self.fill_login_form()
                        self.click_login_button()
                    continue

            except Exception as e:
                self.logger.error(f"验证码处理第{attempt + 1}次尝试失败: {str(e)}")
                if attempt < self.MAX_RETRIES - 1:
                    self.logger.info(f"{self.RETRY_DELAY}秒后重试...")
                    time.sleep(self.RETRY_DELAY)

        self.logger.error("验证码处理失败，已达到最大重试次数")
        return False

    def take_screenshot(self):
        """对网页上半部分进行截图"""
        try:
            self.logger.info("开始截图...")

            # 获取页面尺寸
            page_height = self.driver.execute_script("return document.body.scrollHeight")
            window_height = self.driver.execute_script("return window.innerHeight")

            self.logger.info(f"页面高度: {page_height}, 窗口高度: {window_height}")

            # 滚动到页面顶部
            self.driver.execute_script("window.scrollTo(0, 0)")
            time.sleep(2)

            # 截取全屏
            screenshot = self.driver.get_screenshot_as_png()

            # 使用PIL处理图片，只保留上半部分
            image = Image.open(io.BytesIO(screenshot))
            width, height = image.size

            # 截取上半部分（使用配置的比例）
            crop_ratio = SCREENSHOT_CONFIG.get("crop_ratio", 0.8)
            crop_height = int(height * crop_ratio)
            upper_half = image.crop((0, 0, width, crop_height))

            # 保存截图 - 使用姓名.png格式
            screenshot_format = "png"  # 固定使用png格式
            screenshot_filename = f"{self.ACCOUNT_NAME}.{screenshot_format}"

            save_dir = SCREENSHOT_CONFIG.get("save_dir", "screenshots")
            if not os.path.exists(save_dir):
                os.makedirs(save_dir)

            screenshot_path = f"{save_dir}/{screenshot_filename}"

            # 直接覆盖同名文件，确保文件名格式为：姓名.png
            upper_half.save(screenshot_path)

            self.logger.info(f"截图保存成功: {screenshot_path}")
            return screenshot_path

        except Exception as e:
            self.logger.error(f"截图失败: {str(e)}")
            return None

    def check_login_success(self):
        """检查登录是否成功"""
        try:
            self.logger.info("检查登录状态...")

            # 等待页面跳转或错误提示
            time.sleep(3)

            # 检查是否还在登录页面
            current_url = self.driver.current_url
            self.logger.info(f"当前页面URL: {current_url}")

            if "login" in current_url:
                # 检查是否有错误提示
                try:
                    error_elements = self.driver.find_elements(By.CSS_SELECTOR, ".error-msg, .alert-danger, .login-error")
                    if error_elements:
                        error_text = error_elements[0].text
                        self.logger.warning(f"登录失败，错误信息: {error_text}")
                        return False
                except:
                    pass

                # 检查验证码是否还存在
                try:
                    captcha_elements = self.driver.find_elements(By.CSS_SELECTOR, ".tc-slider-normal")
                    if captcha_elements:
                        self.logger.warning("验证码验证失败，需要重试")
                        return False
                except:
                    pass

                self.logger.warning("仍在登录页面，登录可能失败")
                return False
            else:
                self.logger.info("登录成功，已跳转到其他页面")
                return True

        except Exception as e:
            self.logger.error(f"检查登录状态失败: {str(e)}")
            return False



    def navigate_to_target_page(self):
        """导航到目标页面"""
        try:
            self.logger.info(f"正在访问目标页面: {self.TARGET_URL}")
            self.driver.get(self.TARGET_URL)

            # 等待页面加载
            time.sleep(5)

            # 检查页面是否正确加载
            current_url = self.driver.current_url
            self.logger.info(f"目标页面加载完成，当前URL: {current_url}")

            return True

        except Exception as e:
            self.logger.error(f"访问目标页面失败: {str(e)}")
            return False



    def cleanup(self):
        """清理资源"""
        try:
            if self.driver:
                self.logger.info("正在关闭浏览器...")
                self.driver.quit()
                self.logger.info("浏览器已关闭")
        except Exception as e:
            self.logger.error(f"清理资源失败: {str(e)}")

    def run(self):
        """主执行方法"""
        try:
            self.logger.info("开始执行自动登录流程...")

            # 1. 初始化浏览器
            self.setup_driver()

            # 2. 访问登录页面
            self.navigate_to_login()

            # 3. 填写登录表单
            self.fill_login_form()

            # 4. 点击登录按钮
            self.click_login_button()

            # 5. 处理验证码
            if not self.handle_captcha_with_retry():
                self.logger.error("验证码处理失败，登录终止")
                return False

            # 6. 等待登录完成
            self.logger.info("等待登录完成...")
            time.sleep(3)

            # 7. 访问目标页面
            if not self.navigate_to_target_page():
                self.logger.error("访问目标页面失败")
                return False

            # 8. 截图
            screenshot_path = self.take_screenshot()
            if screenshot_path:
                self.logger.info(f"任务完成！截图已保存: {screenshot_path}")
                return True
            else:
                self.logger.error("截图失败")
                return False

        except Exception as e:
            self.logger.error(f"执行过程中发生错误: {str(e)}")
            return False
        finally:
            # 清理资源
            self.cleanup()


def load_accounts_from_txt(file_path="accounts.txt"):
    """从txt文件加载账号信息
    文件格式：每行一个账号，格式为：姓名 账号 密码
    """
    accounts = []
    try:
        if not os.path.exists(file_path):
            print(f"⚠️  账号文件 {file_path} 不存在")
            return accounts

        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()


        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            if not line or line.startswith('#'):  # 跳过空行和注释行
                continue

            parts = line.split()
            if len(parts) >= 3:
                name = parts[0]
                username = parts[1]
                password = ' '.join(parts[2:])  # 密码可能包含空格

                accounts.append({
                    "username": username,
                    "password": password,
                    "name": name,
                    "enabled": True
                })
                print(f"✅ 加载账号: {name} ({username})")
            else:
                print(f"⚠️  第{line_num}行格式错误，跳过: {line}")

        print(f"📋 从 {file_path} 成功加载 {len(accounts)} 个账号")
        return accounts

    except Exception as e:
        print(f"❌ 加载账号文件失败: {str(e)}")
        return accounts

class BatchProcessor:
    """批量处理器"""
    def __init__(self):
        self.results = []
        self.total_accounts = 0
        self.success_count = 0
        self.failed_count = 0

    def process_accounts_list(self, accounts_list):
        """批量处理账号列表"""
        print("=" * 80)
        print("智慧中小学自动登录脚本 - 批量处理模式")
        print("功能：批量自动登录并截图指定页面（手动验证码）")
        print("=" * 80)

        self.total_accounts = len(accounts_list)

        if self.total_accounts == 0:
            print("❌ 没有找到可用的账号")
            return

        print(f"📋 找到 {self.total_accounts} 个账号")
        print("🔧 验证码模式：手动操作")
        print("📸 截图命名：使用姓名")
        print("=" * 80)

        # 处理每个账号
        for index, account_info in enumerate(accounts_list):
            account_name = account_info.get("name", f"账号{index + 1}")

            print(f"\n{'='*60}")
            print(f"🚀 开始处理第 {index + 1}/{self.total_accounts} 个账号: {account_name}")
            print(f"{'='*60}")

            try:
                # 创建自动登录实例
                auto_login = SmartEduAutoLogin(account_info, index)

                # 执行登录流程
                success = auto_login.run()

                # 记录结果
                result = {
                    "account_name": account_name,
                    "username": account_info["username"],
                    "success": success,
                    "index": index + 1
                }
                self.results.append(result)

                if success:
                    self.success_count += 1
                    print(f"✅ 账号 {account_name} 处理成功！")
                else:
                    self.failed_count += 1
                    print(f"❌ 账号 {account_name} 处理失败！")

            except KeyboardInterrupt:
                print(f"\n⚠️  用户中断执行，停止处理账号 {account_name}")
                break
            except Exception as e:
                self.failed_count += 1
                print(f"💥 账号 {account_name} 处理异常: {str(e)}")
                result = {
                    "account_name": account_name,
                    "username": account_info["username"],
                    "success": False,
                    "error": str(e),
                    "index": index + 1
                }
                self.results.append(result)

                # 根据配置决定是否继续处理下一个账号
                if not BATCH_CONFIG.get("continue_on_error", True):
                    print("⚠️  配置为遇到错误时停止，终止批量处理")
                    break

            # 账号之间的间隔
            if index < self.total_accounts - 1:  # 不是最后一个账号
                delay = BATCH_CONFIG.get("account_delay", 5)
                print(f"⏰ 等待 {delay} 秒后处理下一个账号...")
                time.sleep(delay)

        # 显示最终结果
        self.show_final_results()

    def show_final_results(self):
        """显示最终处理结果"""
        print("\n" + "="*80)
        print("📊 批量处理结果汇总")
        print("="*80)
        print(f"总账号数: {self.total_accounts}")
        print(f"成功数量: {self.success_count}")
        print(f"失败数量: {self.failed_count}")
        print(f"成功率: {(self.success_count/self.total_accounts*100):.1f}%" if self.total_accounts > 0 else "0%")
        print("-"*80)

        # 详细结果
        for result in self.results:
            status = "✅ 成功" if result["success"] else "❌ 失败"
            print(f"{result['index']:2d}. {result['account_name']:15s} ({result['username']:15s}) - {status}")
            if not result["success"] and "error" in result:
                print(f"    错误: {result['error']}")

        print("="*80)
        print("📁 请查看 screenshots 文件夹中的截图")
        print("📋 详细日志请查看 logs 文件夹")
        print("="*80)

def main():
    """主函数"""
    try:
        print("=" * 80)
        print("智慧中小学自动登录脚本 - 批量处理版")
        print("=" * 80)
        print("请选择账号来源:")
        print("1. 使用配置文件 (config.py)")
        print("2. 使用txt文件 (accounts.txt)")
        print("3. 退出")
        print("=" * 80)

        choice = input("请输入选择 (1-3): ").strip()

        accounts_list = []

        if choice == "1":
            # 从配置文件加载
            accounts_list = [acc for acc in ACCOUNTS_CONFIG if acc.get("enabled", True)]
            if not accounts_list:
                print("❌ 配置文件中没有找到启用的账号")
                return
        elif choice == "2":
            # 从txt文件加载
            accounts_list = load_accounts_from_txt("accounts.txt")
            if not accounts_list:
                print("❌ 无法从txt文件加载账号")
                return
        elif choice == "3":
            print("👋 再见！")
            return
        else:
            print("❌ 无效选择")
            return

        # 根据账号数量选择处理模式
        if len(accounts_list) > 1:
            # 批量处理模式
            processor = BatchProcessor()
            processor.process_accounts_list(accounts_list)
        elif len(accounts_list) == 1:
            # 单账号处理模式
            print("=" * 60)
            print("智慧中小学自动登录脚本 - 单账号模式")
            print("功能：自动登录并截图指定页面（手动验证码）")
            print("=" * 60)

            account_info = accounts_list[0]
            auto_login = SmartEduAutoLogin(account_info, 0)

            success = auto_login.run()

            if success:
                print("\n✅ 任务执行成功！")
                print("📁 请查看 screenshots 文件夹中的截图")
                print("📋 详细日志请查看 logs 文件夹")
            else:
                print("\n❌ 任务执行失败！")
                print("📋 请查看日志文件了解详细错误信息")
        else:
            print("❌ 没有找到可用的账号")

    except KeyboardInterrupt:
        print("\n⚠️  用户中断执行")
    except Exception as e:
        print(f"\n💥 程序异常: {str(e)}")


if __name__ == "__main__":
    main()







