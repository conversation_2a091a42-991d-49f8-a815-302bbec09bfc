@echo off
chcp 65001 >nul
title ChromeDriver自动下载工具

echo.
echo ========================================
echo    ChromeDriver 自动下载工具
echo ========================================
echo.

echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到Python环境
    echo 请先安装Python 3.7或更高版本
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

echo 正在运行ChromeDriver下载脚本...
python download_chromedriver.py

echo.
echo 按任意键退出...
pause >nul
