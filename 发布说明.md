# 智慧中小学自动登录工具 - 发布说明

## 📦 发布包内容

本发布包包含以下文件：

### 主程序
- `智慧中小学自动登录工具.exe` - 主程序（GUI版本）
- `启动程序.bat` - 快速启动脚本

### ChromeDriver相关
- `chromedriver.exe` - Chrome浏览器驱动（如果已下载）
- `下载ChromeDriver.exe` - ChromeDriver自动下载工具
- `下载ChromeDriver.bat` - ChromeDriver下载脚本
- `测试ChromeDriver.exe` - ChromeDriver测试工具
- `ChromeDriver安装说明.txt` - 详细安装说明
- `ChromeDriver问题解决方案.md` - 问题解决指南

### 配置和文档
- `config_example.py` - 配置文件示例
- `accounts_example.txt` - 账号文件示例
- `使用说明_GUI版.md` - GUI版本使用说明
- `README.md` - 完整使用说明
- `requirements.txt` - Python依赖列表

## 🚀 快速开始

### 1. 首次使用
1. 解压发布包到任意目录
2. 双击运行 `智慧中小学自动登录工具.exe`
3. 如果提示ChromeDriver错误，请看下面的解决方案

### 2. ChromeDriver问题解决
如果程序提示找不到ChromeDriver，请按以下步骤操作：

**方法一：自动下载（推荐）**
1. 双击运行 `下载ChromeDriver.exe`
2. 等待自动下载完成
3. 重新启动主程序

**方法二：使用批处理脚本**
1. 双击运行 `下载ChromeDriver.bat`
2. 按提示操作
3. 重新启动主程序

**方法三：测试验证**
1. 双击运行 `测试ChromeDriver.exe`
2. 查看测试结果
3. 根据提示进行修复

### 3. 添加账号
1. 启动主程序
2. 点击"➕ 添加账号"按钮
3. 填写姓名、账号、密码
4. 点击确定保存

### 4. 开始使用
1. 确保已添加账号
2. 点击"🚀 开始执行"
3. 在倒计时期间手动完成验证码
4. 等待程序自动完成后续操作

## ⚠️ 重要提示

### 系统要求
- Windows 7/8/10/11
- 已安装Chrome浏览器
- 稳定的网络连接

### 使用注意事项
1. **Chrome浏览器**：必须安装Chrome浏览器
2. **ChromeDriver版本**：需要与Chrome版本匹配
3. **网络环境**：确保网络连接稳定
4. **验证码操作**：需要手动完成滑块验证码
5. **账号安全**：请妥善保管账号密码

### 常见问题
1. **程序无法启动**：检查是否有杀毒软件拦截
2. **ChromeDriver错误**：按照上述方法下载驱动
3. **登录失败**：检查账号密码是否正确
4. **网络超时**：检查网络连接或更换网络环境

## 📁 文件夹说明

程序运行后会自动创建以下文件夹：

- `logs/` - 运行日志文件
- `screenshots/` - 截图文件
- `accounts.json` - 账号配置文件（自动生成）

## 🔧 高级配置

如需修改高级设置，可以：
1. 复制 `config_example.py` 为 `config.py`
2. 根据需要修改配置参数
3. 重新启动程序

## 📞 技术支持

如遇到问题，请：
1. 查看 `logs/` 文件夹中的日志文件
2. 阅读 `ChromeDriver问题解决方案.md`
3. 运行 `测试ChromeDriver.exe` 进行诊断

## 📄 免责声明

本工具仅供学习和研究使用，使用者需遵守相关网站的使用条款和法律法规。作者不承担因使用本工具而产生的任何责任。

## 🔄 更新说明

### v2.0 新特性
- ✅ 现代化GUI界面
- ✅ 内置ChromeDriver管理
- ✅ 自动下载工具
- ✅ 完善的错误处理
- ✅ 详细的使用说明

### 解决的问题
- 🔧 修复了ChromeDriver兼容性问题
- 🔧 改进了错误提示信息
- 🔧 增加了自动下载功能
- 🔧 优化了用户体验
