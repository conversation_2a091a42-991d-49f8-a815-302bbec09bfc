# 智慧中小学自动登录工具 - GUI版本

## 🎯 项目简介

这是一个现代化的GUI版本智慧中小学自动登录工具，基于Python和CustomTkinter开发，提供友好的图形界面和强大的批量处理功能。

## ✨ 主要特性

### 🖥️ 现代化界面
- 基于CustomTkinter的美观现代界面
- 响应式布局，支持窗口缩放
- 直观的操作按钮和状态显示

### 👥 账号管理
- 可视化账号添加、编辑、删除
- 支持启用/禁用账号
- 批量导入txt格式账号文件
- 账号信息本地JSON存储

### ⏰ 智能倒计时
- 验证码操作时的可视化倒计时
- 可自定义倒计时时间
- 进度条显示剩余时间
- 颜色变化提醒（绿→橙→红）

### 🎮 灵活控制
- 开始/暂停/停止操作
- 实时状态显示
- 进度跟踪
- 错误处理和重试机制

### 📊 日志系统
- 实时日志显示
- 详细操作记录
- 错误信息追踪
- 独立日志文件保存

### 📸 自动截图
- 登录成功后自动截图
- 按姓名命名（姓名.png）
- 智能裁剪页面上半部分

## 📁 项目结构

```
智慧中小学登陆/
├── gui_main.py                    # GUI主程序
├── gui_login_core.py              # 登录核心模块
├── 智慧中小学自动登录.py           # 原命令行版本
├── config.py                      # 配置文件
├── accounts.txt                   # 账号文件（命令行版本）
├── requirements.txt               # Python依赖
├── 使用说明_GUI版.md              # GUI版使用说明
├── install_dependencies.bat       # 依赖安装脚本
├── start_gui.bat                  # GUI启动脚本
├── build_exe.bat                  # 简单打包脚本
├── create_release.bat             # 完整发布脚本
├── build_spec.py                  # PyInstaller配置生成器
├── version_info.txt               # 版本信息文件
├── screenshots/                   # 截图保存目录
└── logs/                          # 日志保存目录
```

## 🚀 快速开始

### 开发环境运行

1. **克隆项目**
   ```bash
   git clone [项目地址]
   cd 智慧中小学登陆
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```
   或运行 `install_dependencies.bat`

3. **启动程序**
   ```bash
   python gui_main.py
   ```
   或运行 `start_gui.bat`

### 打包发布

1. **生成可执行文件**
   ```bash
   # 方式1：使用完整发布脚本（推荐）
   create_release.bat
   
   # 方式2：使用简单打包脚本
   build_exe.bat
   
   # 方式3：手动打包
   python build_spec.py
   pyinstaller gui_app.spec
   ```

2. **发布包内容**
   - `智慧中小学自动登录工具.exe` - 主程序
   - `使用说明_GUI版.md` - 使用说明
   - `install_dependencies.bat` - 依赖安装脚本
   - `accounts_example.txt` - 账号文件示例
   - `screenshots/` - 截图目录
   - `logs/` - 日志目录

## 🛠️ 技术架构

### 核心技术栈
- **GUI框架**: CustomTkinter 5.2+
- **浏览器自动化**: Selenium 4.15+
- **图像处理**: Pillow 10.0+
- **多线程**: Python threading
- **数据存储**: JSON文件

### 架构设计
```
GUI界面层 (gui_main.py)
    ↓
回调管理层 (CallbackManager)
    ↓
登录核心层 (gui_login_core.py)
    ↓
浏览器驱动层 (Selenium WebDriver)
```

### 关键特性
- **多线程设计**: 避免界面卡顿
- **事件驱动**: 基于回调的状态更新
- **模块化**: 核心逻辑与界面分离
- **异常处理**: 完善的错误处理机制

## 📋 使用指南

### 账号管理
1. 点击"➕ 添加账号"添加新账号
2. 选择账号后点击"✏️ 编辑"修改信息
3. 点击"🗑️ 删除"移除不需要的账号
4. 点击"📁 导入"批量导入txt格式账号

### 执行任务
1. 在右侧设置验证码倒计时时间
2. 点击"🚀 开始执行"启动批量处理
3. 验证码出现时在倒计时内完成操作
4. 可随时暂停/继续或停止执行

### 查看结果
- 实时日志显示在左下角
- 执行状态显示在右侧
- 截图保存在screenshots文件夹
- 详细日志保存在logs文件夹

## 🔧 配置说明

### 倒计时设置
- 默认10秒，可根据操作速度调整
- 建议范围：5-30秒

### 账号文件格式
```
姓名 账号 密码
张三 13800138000 password123
李四 13900139000 mypassword
```

### 浏览器设置
- 自动检测Chrome浏览器
- 自动下载匹配的ChromeDriver
- 支持反检测措施

## 🐛 故障排除

### 常见问题
1. **程序无法启动**: 检查Python环境和依赖
2. **Chrome问题**: 确保Chrome浏览器已安装
3. **验证码失败**: 增加倒计时时间
4. **登录失败**: 检查账号密码正确性

### 调试方法
1. 查看实时日志显示
2. 检查logs文件夹中的详细日志
3. 确认网络连接正常
4. 更新Chrome浏览器版本

## 📈 版本历史

### v2.0 (GUI版本) - 2025-01-24
- ✨ 全新GUI界面设计
- 🎮 可视化操作控制
- ⏰ 智能倒计时功能
- 📊 实时状态和日志显示
- 👥 完善的账号管理系统
- 📦 一键打包发布

### v1.0 (命令行版本)
- 基础自动登录功能
- 批量处理支持
- 截图保存功能
- 配置文件管理

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进项目！

### 开发环境设置
1. Fork项目
2. 创建功能分支
3. 提交更改
4. 创建Pull Request

## 📄 许可证

本项目仅供学习和研究使用，请遵守相关法律法规。

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至项目维护者

---

**注意**: 本工具仅用于自动化操作，请确保遵守网站的使用条款和相关法律法规。
