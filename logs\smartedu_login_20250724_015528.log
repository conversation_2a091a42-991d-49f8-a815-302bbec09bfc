2025-07-24 01:55:28,285 - INFO - ==================================================
2025-07-24 01:55:28,285 - INFO - 智慧中小学自动登录脚本启动
2025-07-24 01:55:28,285 - INFO - 配置信息 - 用户名: 15629266999
2025-07-24 01:55:28,285 - INFO - 配置信息 - 登录URL: https://auth.smartedu.cn/uias/login
2025-07-24 01:55:28,286 - INFO - 配置信息 - 目标URL: https://basic.smartedu.cn/training/2025sqpx
2025-07-24 01:55:28,286 - INFO - ==================================================
2025-07-24 01:55:28,286 - INFO - 开始执行自动登录流程...
2025-07-24 01:55:28,286 - INFO - 正在初始化Chrome浏览器...
2025-07-24 01:55:32,431 - INFO - Chrome浏览器初始化成功
2025-07-24 01:55:32,432 - INFO - 正在访问登录页面: https://auth.smartedu.cn/uias/login
2025-07-24 01:55:33,260 - INFO - 登录页面加载成功
2025-07-24 01:55:34,855 - INFO - 开始填写登录表单...
2025-07-24 01:55:36,211 - INFO - 用户名输入完成
2025-07-24 01:55:38,604 - INFO - 密码输入完成
2025-07-24 01:55:39,903 - INFO - 已勾选同意协议
2025-07-24 01:55:40,779 - INFO - 点击登录按钮...
2025-07-24 01:55:41,591 - INFO - 登录按钮已点击
2025-07-24 01:55:41,591 - INFO - 等待验证码加载...
2025-07-24 01:55:44,606 - INFO - 检测到腾讯验证码元素: #tcaptcha_iframe_dy
2025-07-24 01:55:44,607 - INFO - 验证码已加载
2025-07-24 01:55:44,607 - INFO - 验证码处理尝试 1/3
2025-07-24 01:55:44,607 - INFO - 正在获取腾讯验证码图片...
2025-07-24 01:55:44,607 - INFO - 等待腾讯验证码iframe加载...
2025-07-24 01:55:49,632 - INFO - 找到验证码iframe，使用选择器: #tcaptcha_iframe_dy
2025-07-24 01:55:49,657 - INFO - 已切换到验证码iframe
2025-07-24 01:55:52,671 - INFO - 在iframe中找到 3 个带背景图的div元素
2025-07-24 01:55:52,687 - INFO - 从tc-bg-img获取到背景图片
2025-07-24 01:55:52,687 - INFO - 使用背景图片作为滑块图片
2025-07-24 01:55:52,688 - INFO - 背景图片URL: https://turing.captcha.qcloud.com/cap_union_new_getcapbysig?img_index=1&image=02610900005c3260000000
2025-07-24 01:55:52,689 - INFO - 滑块图片URL: https://turing.captcha.qcloud.com/cap_union_new_getcapbysig?img_index=1&image=02610900005c3260000000
2025-07-24 01:55:53,029 - INFO - 图片下载并编码成功，大小: 37597 bytes
2025-07-24 01:55:53,361 - INFO - 图片下载并编码成功，大小: 37597 bytes
2025-07-24 01:55:53,364 - INFO - 正在调用第三方API识别验证码...
2025-07-24 01:55:53,674 - INFO - 验证码识别API响应: {'msg': 'token有误，无此访问权限！', 'code': 10003, 'data': []}
2025-07-24 01:55:53,674 - ERROR - 验证码识别失败: 验证码识别失败: token有误，无此访问权限！
2025-07-24 01:55:53,675 - ERROR - 验证码处理第1次尝试失败: 验证码识别失败: token有误，无此访问权限！
2025-07-24 01:55:53,675 - INFO - 2秒后重试...
2025-07-24 01:55:55,676 - INFO - 验证码处理尝试 2/3
2025-07-24 01:55:55,677 - INFO - 正在获取腾讯验证码图片...
2025-07-24 01:55:55,678 - INFO - 等待腾讯验证码iframe加载...
2025-07-24 01:56:00,372 - INFO - 正在关闭浏览器...
2025-07-24 01:56:00,376 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))': /session/a73d903fc370f4ea344744dc788ce03b
2025-07-24 01:56:04,472 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000022C1C7B7230>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /session/a73d903fc370f4ea344744dc788ce03b
2025-07-24 01:56:08,557 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000022C1C7B7140>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /session/a73d903fc370f4ea344744dc788ce03b
2025-07-24 01:56:16,767 - INFO - 浏览器已关闭
2025-07-24 01:56:16,768 - INFO - 正在关闭浏览器...
2025-07-24 01:56:20,840 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000022C1C7B7DD0>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /session/a73d903fc370f4ea344744dc788ce03b
2025-07-24 01:56:24,897 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000022C1C7FC170>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /session/a73d903fc370f4ea344744dc788ce03b
2025-07-24 01:56:28,991 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x0000022C1C7FC320>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /session/a73d903fc370f4ea344744dc788ce03b
2025-07-24 01:56:37,173 - INFO - 浏览器已关闭
