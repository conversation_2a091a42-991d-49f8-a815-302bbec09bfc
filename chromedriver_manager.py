#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ChromeDriver管理器
负责检测、下载和管理ChromeDriver
"""

import os
import sys
import requests
import zipfile
import subprocess
import json
from pathlib import Path

class ChromeDriverManager:
    """ChromeDriver管理器"""
    
    def __init__(self):
        self.app_dir = self.get_app_dir()
        self.chromedriver_path = os.path.join(self.app_dir, "chromedriver.exe")
        
    def get_app_dir(self):
        """获取应用程序目录"""
        if getattr(sys, 'frozen', False):
            # 打包后的exe文件
            return os.path.dirname(sys.executable)
        else:
            # Python脚本
            return os.path.dirname(os.path.abspath(__file__))
    
    def is_chromedriver_available(self):
        """检查ChromeDriver是否可用"""
        return os.path.exists(self.chromedriver_path)
    
    def get_chrome_version(self):
        """获取Chrome版本"""
        try:
            chrome_paths = [
                r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe")
            ]
            
            for chrome_path in chrome_paths:
                if os.path.exists(chrome_path):
                    # 使用wmic获取版本
                    try:
                        result = subprocess.run([
                            'wmic', 'datafile', 'where', 
                            f'name="{chrome_path.replace(chr(92), chr(92)+chr(92))}"',
                            'get', 'Version', '/value'
                        ], capture_output=True, text=True, timeout=10)
                        
                        if result.returncode == 0:
                            for line in result.stdout.split('\n'):
                                if 'Version=' in line:
                                    version = line.split('=')[1].strip()
                                    if version:
                                        return version
                    except:
                        pass
            
            # 备用方法：注册表
            try:
                import winreg
                key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, r"Software\Google\Chrome\BLBeacon")
                version, _ = winreg.QueryValueEx(key, "version")
                winreg.CloseKey(key)
                return version
            except:
                pass
                
            return None
            
        except Exception:
            return None
    
    def get_chromedriver_version(self, chrome_version):
        """获取对应的ChromeDriver版本"""
        try:
            if not chrome_version:
                url = "https://chromedriver.storage.googleapis.com/LATEST_RELEASE"
            else:
                major_version = chrome_version.split('.')[0]
                url = f"https://chromedriver.storage.googleapis.com/LATEST_RELEASE_{major_version}"
            
            response = requests.get(url, timeout=10)
            if response.status_code == 200:
                return response.text.strip()
            else:
                # 备用：获取最新版本
                response = requests.get("https://chromedriver.storage.googleapis.com/LATEST_RELEASE", timeout=10)
                return response.text.strip() if response.status_code == 200 else None
                
        except Exception:
            return None
    
    def download_chromedriver(self, version):
        """下载ChromeDriver"""
        try:
            download_url = f"https://chromedriver.storage.googleapis.com/{version}/chromedriver_win32.zip"
            
            # 下载文件
            response = requests.get(download_url, timeout=30)
            if response.status_code != 200:
                return False
            
            # 保存并解压
            zip_path = os.path.join(self.app_dir, "chromedriver_temp.zip")
            with open(zip_path, 'wb') as f:
                f.write(response.content)
            
            # 解压
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(self.app_dir)
            
            # 删除临时文件
            os.remove(zip_path)
            
            return os.path.exists(self.chromedriver_path)
            
        except Exception:
            return False
    
    def auto_setup_chromedriver(self, callback=None):
        """自动设置ChromeDriver"""
        try:
            if callback:
                callback("检查ChromeDriver...")
            
            # 如果已存在，检查是否需要更新
            if self.is_chromedriver_available():
                if callback:
                    callback("ChromeDriver已存在")
                return True
            
            if callback:
                callback("检测Chrome版本...")
            
            # 获取Chrome版本
            chrome_version = self.get_chrome_version()
            if not chrome_version:
                if callback:
                    callback("无法检测Chrome版本，请确保Chrome已安装")
                return False
            
            if callback:
                callback(f"Chrome版本: {chrome_version}")
            
            # 获取对应的ChromeDriver版本
            driver_version = self.get_chromedriver_version(chrome_version)
            if not driver_version:
                if callback:
                    callback("无法获取ChromeDriver版本信息")
                return False
            
            if callback:
                callback(f"下载ChromeDriver {driver_version}...")
            
            # 下载ChromeDriver
            success = self.download_chromedriver(driver_version)
            
            if success:
                if callback:
                    callback("ChromeDriver下载成功！")
                return True
            else:
                if callback:
                    callback("ChromeDriver下载失败")
                return False
                
        except Exception as e:
            if callback:
                callback(f"设置ChromeDriver失败: {str(e)}")
            return False
    
    def get_chromedriver_path(self):
        """获取ChromeDriver路径"""
        if self.is_chromedriver_available():
            return self.chromedriver_path
        return None

# 全局实例
chromedriver_manager = ChromeDriverManager()
