#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建发布包脚本
"""

import os
import shutil

def create_release_package():
    """创建完整的发布包"""
    print("🚀 开始创建发布包...")
    
    # 创建release目录
    release_dir = "release"
    if os.path.exists(release_dir):
        shutil.rmtree(release_dir)
    os.makedirs(release_dir)
    
    # 复制主程序
    print("📦 复制主程序...")
    if os.path.exists("dist/智慧中小学自动登录工具.exe"):
        shutil.copy("dist/智慧中小学自动登录工具.exe", f"{release_dir}/")
        print("✅ 主程序复制完成")
    else:
        print("❌ 主程序不存在，请先运行打包")
        return False
    
    # 复制文档文件
    print("📄 复制文档文件...")
    docs_to_copy = [
        "使用说明_GUI版.md",
        "requirements.txt"
    ]
    
    for doc in docs_to_copy:
        if os.path.exists(doc):
            shutil.copy(doc, f"{release_dir}/")
            print(f"✅ 已复制: {doc}")
    
    # 复制脚本文件
    print("📜 复制脚本文件...")
    scripts_to_copy = [
        "install_dependencies.bat",
        "start_gui.bat"
    ]
    
    for script in scripts_to_copy:
        if os.path.exists(script):
            shutil.copy(script, f"{release_dir}/")
            print(f"✅ 已复制: {script}")
    
    # 创建目录结构
    print("📁 创建目录结构...")
    dirs_to_create = ["screenshots", "logs"]
    for dir_name in dirs_to_create:
        dir_path = f"{release_dir}/{dir_name}"
        os.makedirs(dir_path, exist_ok=True)
        print(f"✅ 已创建目录: {dir_name}")
    
    # 获取文件大小
    exe_path = f"{release_dir}/智慧中小学自动登录工具.exe"
    if os.path.exists(exe_path):
        size_mb = os.path.getsize(exe_path) / (1024 * 1024)
        print(f"📊 主程序大小: {size_mb:.1f} MB")
    
    print("\n🎉 发布包创建完成！")
    print("=" * 50)
    print(f"📂 发布文件夹: {release_dir}/")
    print("📋 包含文件:")
    
    # 列出所有文件
    for root, dirs, files in os.walk(release_dir):
        level = root.replace(release_dir, '').count(os.sep)
        indent = ' ' * 2 * level
        print(f"{indent}{os.path.basename(root)}/")
        subindent = ' ' * 2 * (level + 1)
        for file in files:
            file_path = os.path.join(root, file)
            file_size = os.path.getsize(file_path)
            if file_size > 1024 * 1024:  # > 1MB
                size_str = f"({file_size / (1024 * 1024):.1f} MB)"
            elif file_size > 1024:  # > 1KB
                size_str = f"({file_size / 1024:.1f} KB)"
            else:
                size_str = f"({file_size} B)"
            print(f"{subindent}{file} {size_str}")
    
    print("=" * 50)
    print("💡 使用提示:")
    print("1. 将整个release文件夹分发给用户")
    print("2. 用户首次使用需运行install_dependencies.bat")
    print("3. 然后可直接运行主程序")
    print("4. 或使用start_gui.bat启动")
    
    return True

if __name__ == "__main__":
    create_release_package()
