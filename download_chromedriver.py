#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ChromeDriver自动下载脚本
自动检测Chrome版本并下载对应的ChromeDriver
"""

import os
import sys
import requests
import zipfile
import json
import subprocess
import re
from pathlib import Path

def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("🚀 ChromeDriver 自动下载工具")
    print("=" * 60)
    print()

def get_chrome_version():
    """获取本地Chrome浏览器版本"""
    try:
        # Windows系统下获取Chrome版本的方法
        chrome_paths = [
            r"C:\Program Files\Google\Chrome\Application\chrome.exe",
            r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
            os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe")
        ]
        
        chrome_path = None
        for path in chrome_paths:
            if os.path.exists(path):
                chrome_path = path
                break
        
        if not chrome_path:
            print("❌ 未找到Chrome浏览器，请先安装Chrome")
            return None
        
        print(f"✅ 找到Chrome浏览器: {chrome_path}")
        
        # 使用wmic命令获取版本信息
        try:
            result = subprocess.run([
                'wmic', 'datafile', 'where', f'name="{chrome_path.replace(chr(92), chr(92)+chr(92))}"',
                'get', 'Version', '/value'
            ], capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                for line in result.stdout.split('\n'):
                    if 'Version=' in line:
                        version = line.split('=')[1].strip()
                        if version:
                            print(f"✅ Chrome版本: {version}")
                            return version
        except:
            pass
        
        # 备用方法：通过注册表获取版本
        try:
            import winreg
            key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, r"Software\Google\Chrome\BLBeacon")
            version, _ = winreg.QueryValueEx(key, "version")
            winreg.CloseKey(key)
            print(f"✅ Chrome版本: {version}")
            return version
        except:
            pass
        
        print("⚠️ 无法自动检测Chrome版本，将使用最新版本")
        return None
        
    except Exception as e:
        print(f"❌ 获取Chrome版本失败: {str(e)}")
        return None

def get_chromedriver_version(chrome_version):
    """根据Chrome版本获取对应的ChromeDriver版本"""
    try:
        if not chrome_version:
            # 如果无法获取Chrome版本，使用最新的ChromeDriver
            print("🔍 获取最新ChromeDriver版本...")
            url = "https://chromedriver.storage.googleapis.com/LATEST_RELEASE"
        else:
            # 获取主版本号
            major_version = chrome_version.split('.')[0]
            print(f"🔍 获取Chrome {major_version}.x 对应的ChromeDriver版本...")
            url = f"https://chromedriver.storage.googleapis.com/LATEST_RELEASE_{major_version}"
        
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            driver_version = response.text.strip()
            print(f"✅ 找到对应ChromeDriver版本: {driver_version}")
            return driver_version
        else:
            print("⚠️ 无法获取ChromeDriver版本信息，使用最新版本")
            response = requests.get("https://chromedriver.storage.googleapis.com/LATEST_RELEASE", timeout=10)
            return response.text.strip() if response.status_code == 200 else None
            
    except Exception as e:
        print(f"❌ 获取ChromeDriver版本失败: {str(e)}")
        return None

def download_chromedriver(version, download_dir="."):
    """下载ChromeDriver"""
    try:
        # ChromeDriver下载URL
        download_url = f"https://chromedriver.storage.googleapis.com/{version}/chromedriver_win32.zip"
        
        print(f"📥 开始下载ChromeDriver {version}...")
        print(f"📥 下载地址: {download_url}")
        
        # 下载文件
        response = requests.get(download_url, timeout=30)
        if response.status_code != 200:
            print(f"❌ 下载失败，HTTP状态码: {response.status_code}")
            return False
        
        # 保存zip文件
        zip_path = os.path.join(download_dir, "chromedriver.zip")
        with open(zip_path, 'wb') as f:
            f.write(response.content)
        
        print(f"✅ 下载完成: {zip_path}")
        
        # 解压文件
        print("📦 正在解压...")
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(download_dir)
        
        # 删除zip文件
        os.remove(zip_path)
        
        # 检查解压结果
        chromedriver_path = os.path.join(download_dir, "chromedriver.exe")
        if os.path.exists(chromedriver_path):
            print(f"✅ ChromeDriver解压成功: {chromedriver_path}")
            return True
        else:
            print("❌ 解压失败，未找到chromedriver.exe")
            return False
            
    except Exception as e:
        print(f"❌ 下载ChromeDriver失败: {str(e)}")
        return False

def main():
    """主函数"""
    print_banner()
    
    try:
        # 获取程序运行目录
        if getattr(sys, 'frozen', False):
            # 如果是打包后的exe文件
            app_dir = os.path.dirname(sys.executable)
        else:
            # 如果是Python脚本
            app_dir = os.path.dirname(os.path.abspath(__file__))
        
        print(f"📁 程序目录: {app_dir}")
        
        # 检查是否已存在ChromeDriver
        existing_driver = os.path.join(app_dir, "chromedriver.exe")
        if os.path.exists(existing_driver):
            print(f"⚠️ 发现已存在的ChromeDriver: {existing_driver}")
            choice = input("是否要重新下载？(y/N): ").strip().lower()
            if choice not in ['y', 'yes']:
                print("👋 取消下载")
                return
        
        # 1. 获取Chrome版本
        chrome_version = get_chrome_version()
        
        # 2. 获取对应的ChromeDriver版本
        driver_version = get_chromedriver_version(chrome_version)
        if not driver_version:
            print("❌ 无法获取ChromeDriver版本信息")
            return
        
        # 3. 下载ChromeDriver
        success = download_chromedriver(driver_version, app_dir)
        
        if success:
            print("\n" + "="*60)
            print("🎉 ChromeDriver下载成功！")
            print("="*60)
            print(f"📁 文件位置: {os.path.join(app_dir, 'chromedriver.exe')}")
            print("✅ 现在可以正常使用智慧中小学自动登录工具了")
            print("="*60)
        else:
            print("\n" + "="*60)
            print("❌ ChromeDriver下载失败！")
            print("="*60)
            print("🔧 请尝试以下解决方案：")
            print("1. 检查网络连接")
            print("2. 手动下载ChromeDriver并放在程序目录下")
            print("3. 确保Chrome浏览器已正确安装")
            print("="*60)
    
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断下载")
    except Exception as e:
        print(f"\n💥 程序异常: {str(e)}")

if __name__ == "__main__":
    main()
