@echo off
chcp 65001 >nul
title 智慧中小学自动登录脚本 - 批量版

echo ========================================
echo 智慧中小学自动登录脚本 - 批量版
echo ========================================
echo.
echo 功能特点:
echo ✅ 支持批量处理多个账号
echo ✅ 手动验证码操作（更稳定）
echo ✅ 快速输入用户名密码
echo ✅ 支持txt文件导入账号
echo ✅ 截图使用姓名命名
echo.
echo 请选择运行模式:
echo 1. 运行批量登录脚本
echo 2. 配置ChromeDriver
echo 3. 查看配置示例
echo 4. 查看账号文件示例
echo 5. 退出
echo.
set /p choice=请输入选择 (1-5):

if "%choice%"=="1" goto run_script
if "%choice%"=="2" goto chromedriver
if "%choice%"=="3" goto config_example
if "%choice%"=="4" goto accounts_example
if "%choice%"=="5" goto exit
echo 无效选择，使用默认模式
goto run_script

:run_script
echo.
echo 正在检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误：未找到Python环境，请先安装Python 3.7+
    pause
    exit /b 1
)

echo Python环境检查通过
echo.

echo 正在检查依赖包...
pip show selenium >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖包...
    pip install selenium pillow
    if errorlevel 1 (
        echo 错误：依赖包安装失败
        pause
        exit /b 1
    )
)

echo 依赖包检查通过
echo.

echo 开始运行批量登录脚本...
echo ========================================
python "智慧中小学自动登录.py"
goto end

:chromedriver
echo.
echo 配置ChromeDriver...
echo ========================================
python "chromedriver_helper.py"
goto end

:config_example
echo.
echo 查看配置示例...
echo ========================================
if exist "config_example.py" (
    type "config_example.py"
    echo.
    echo 请复制 config_example.py 为 config.py 并修改配置
) else (
    echo 配置示例文件不存在
)
pause
goto end

:accounts_example
echo.
echo 查看账号文件示例...
echo ========================================
if exist "accounts_example.txt" (
    type "accounts_example.txt"
    echo.
    echo 请复制 accounts_example.txt 为 accounts.txt 并修改账号信息
) else (
    echo 账号示例文件不存在
)
pause
goto end

:exit
echo.
echo 👋 再见！
goto end

:end
echo.
echo ========================================
echo 脚本执行完成
echo 请查看logs文件夹中的日志文件了解详细信息
echo 请查看screenshots文件夹中的截图文件
echo ========================================
pause
