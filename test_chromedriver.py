#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ChromeDriver测试脚本
用于验证ChromeDriver是否正常工作
"""

import os
import sys
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service

def print_banner():
    """打印横幅"""
    print("=" * 60)
    print("🧪 ChromeDriver 测试工具")
    print("=" * 60)
    print()

def get_chromedriver_path():
    """获取ChromeDriver路径"""
    try:
        # 获取程序运行目录
        if getattr(sys, 'frozen', False):
            app_dir = os.path.dirname(sys.executable)
        else:
            app_dir = os.path.dirname(os.path.abspath(__file__))
        
        # 内置驱动路径
        bundled_paths = [
            os.path.join(app_dir, "chromedriver.exe"),
            os.path.join(app_dir, "drivers", "chromedriver.exe"),
            os.path.join(app_dir, "bin", "chromedriver.exe"),
        ]
        
        # 检查内置驱动
        for path in bundled_paths:
            if os.path.exists(path):
                print(f"✅ 找到ChromeDriver: {path}")
                return path
        
        # 检查其他路径
        fallback_paths = [
            "chromedriver.exe",
            "./chromedriver.exe",
        ]
        
        for path in fallback_paths:
            if os.path.exists(path):
                print(f"✅ 找到ChromeDriver: {path}")
                return path
        
        print("❌ 未找到ChromeDriver")
        return None
        
    except Exception as e:
        print(f"❌ 获取ChromeDriver路径失败: {str(e)}")
        return None

def test_chromedriver():
    """测试ChromeDriver"""
    print("🔧 开始测试ChromeDriver...")
    
    try:
        # 获取ChromeDriver路径
        chromedriver_path = get_chromedriver_path()
        
        # 设置Chrome选项
        chrome_options = Options()
        chrome_options.add_argument('--headless')  # 无头模式，不显示浏览器窗口
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        
        # 创建WebDriver实例
        if chromedriver_path:
            service = Service(chromedriver_path)
            driver = webdriver.Chrome(service=service, options=chrome_options)
            print(f"✅ 使用指定ChromeDriver成功: {chromedriver_path}")
        else:
            driver = webdriver.Chrome(options=chrome_options)
            print("✅ 使用系统ChromeDriver成功")
        
        # 测试访问网页
        print("🌐 测试访问百度首页...")
        driver.get("https://www.baidu.com")
        
        # 获取页面标题
        title = driver.title
        print(f"✅ 页面标题: {title}")
        
        # 关闭浏览器
        driver.quit()
        print("✅ 浏览器已关闭")
        
        print("\n" + "="*60)
        print("🎉 ChromeDriver测试成功！")
        print("✅ 您的ChromeDriver配置正常，可以正常使用智慧中小学自动登录工具")
        print("="*60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ ChromeDriver测试失败: {str(e)}")
        print("\n" + "="*60)
        print("🔧 解决方案：")
        print("1. 确保Chrome浏览器已安装")
        print("2. 运行 '下载ChromeDriver.exe' 下载驱动")
        print("3. 或手动下载ChromeDriver并放在程序目录下")
        print("4. 查看 'ChromeDriver问题解决方案.md' 获取详细帮助")
        print("="*60)
        
        return False

def main():
    """主函数"""
    print_banner()
    
    try:
        success = test_chromedriver()
        
        if not success:
            print("\n按回车键退出...")
            input()
            
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
    except Exception as e:
        print(f"\n💥 程序异常: {str(e)}")

if __name__ == "__main__":
    main()
