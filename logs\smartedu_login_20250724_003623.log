2025-07-24 00:36:23,781 - INFO - ==================================================
2025-07-24 00:36:23,782 - INFO - 智慧中小学自动登录脚本启动
2025-07-24 00:36:23,782 - INFO - 配置信息 - 用户名: 18971896611
2025-07-24 00:36:23,782 - INFO - 配置信息 - 登录URL: https://auth.smartedu.cn/uias/login
2025-07-24 00:36:23,782 - INFO - 配置信息 - 目标URL: https://basic.smartedu.cn/training/2025sqpx
2025-07-24 00:36:23,782 - INFO - ==================================================
2025-07-24 00:36:23,783 - INFO - 开始执行自动登录流程...
2025-07-24 00:36:23,783 - INFO - 正在初始化Chrome浏览器...
2025-07-24 00:36:28,255 - INFO - Chrome浏览器初始化成功
2025-07-24 00:36:28,256 - INFO - 正在访问登录页面: https://auth.smartedu.cn/uias/login
2025-07-24 00:36:29,012 - INFO - 登录页面加载成功
2025-07-24 00:36:30,956 - INFO - 开始填写登录表单...
2025-07-24 00:36:32,400 - INFO - 用户名输入完成
2025-07-24 00:36:35,098 - INFO - 密码输入完成
2025-07-24 00:36:35,831 - INFO - 已勾选同意协议
2025-07-24 00:36:36,528 - INFO - 点击登录按钮...
2025-07-24 00:36:37,347 - INFO - 登录按钮已点击
2025-07-24 00:36:37,347 - INFO - 等待验证码加载...
2025-07-24 00:36:40,360 - INFO - 检测到腾讯验证码元素: #tcaptcha_iframe_dy
2025-07-24 00:36:40,360 - INFO - 验证码已加载
2025-07-24 00:36:40,360 - INFO - 验证码处理尝试 1/3
2025-07-24 00:36:40,360 - INFO - 正在获取腾讯验证码图片...
2025-07-24 00:36:40,361 - INFO - 等待腾讯验证码iframe加载...
2025-07-24 00:36:45,368 - INFO - 找到验证码iframe，使用选择器: #tcaptcha_iframe_dy
2025-07-24 00:36:45,382 - INFO - 已切换到验证码iframe
2025-07-24 00:36:48,408 - INFO - 在iframe中找到 3 个带背景图的div元素
2025-07-24 00:36:48,436 - INFO - 从tc-bg-img获取到背景图片
2025-07-24 00:36:48,437 - INFO - 使用背景图片作为滑块图片
2025-07-24 00:36:48,438 - INFO - 背景图片URL: https://turing.captcha.qcloud.com/cap_union_new_getcapbysig?img_index=1&image=0261090000323214000000
2025-07-24 00:36:48,438 - INFO - 滑块图片URL: https://turing.captcha.qcloud.com/cap_union_new_getcapbysig?img_index=1&image=0261090000323214000000
2025-07-24 00:36:48,790 - INFO - 图片下载并编码成功，大小: 26388 bytes
2025-07-24 00:36:49,149 - INFO - 图片下载并编码成功，大小: 26388 bytes
2025-07-24 00:36:49,151 - INFO - 正在调用第三方API识别验证码...
2025-07-24 00:36:49,964 - INFO - 验证码识别API响应: {'msg': '识别成功', 'code': 10000, 'data': {'code': 0, 'data': '1', 'time': 0.3320121765136719, 'unique_code': 'c944cf971415cc5ab6561620f6c84bb9'}}
2025-07-24 00:36:49,965 - INFO - 验证码识别成功，X坐标: 1
2025-07-24 00:36:49,966 - INFO - 开始执行腾讯验证码滑块拖拽，目标位置: 1
2025-07-24 00:36:50,000 - INFO - 切换到验证码iframe查找滑块
2025-07-24 00:36:50,009 - INFO - 在iframe中找到滑块元素: .tc-slider-normal
2025-07-24 00:36:50,022 - INFO - 滑块当前位置: 28.75, 宽度: 54
2025-07-24 00:36:50,023 - INFO - 调整后的移动距离: 50
2025-07-24 00:36:50,023 - INFO - 执行真实鼠标拖拽...
2025-07-24 00:36:56,495 - INFO - 真实鼠标拖拽操作完成，等待结果...
2025-07-24 00:36:58,702 - WARNING - 拖拽可能失败，移动距离: 0.0 像素
2025-07-24 00:36:58,702 - INFO - 滑块拖拽操作完成
2025-07-24 00:37:01,705 - INFO - 检查登录状态...
2025-07-24 00:37:04,720 - INFO - 当前页面URL: https://auth.smartedu.cn/uias/login
2025-07-24 00:37:04,752 - WARNING - 仍在登录页面，登录可能失败
2025-07-24 00:37:04,753 - WARNING - 验证码验证失败，2秒后重试...
2025-07-24 00:37:06,045 - INFO - 正在关闭浏览器...
2025-07-24 00:37:06,049 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'ProtocolError('Connection aborted.', ConnectionResetError(10054, '远程主机强迫关闭了一个现有的连接。', None, 10054, None))': /session/e0c613b78d6f992dc6b0b2d03d8f4c3b
2025-07-24 00:37:12,204 - INFO - 正在关闭浏览器...
2025-07-24 00:37:16,311 - WARNING - Retrying (Retry(total=2, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002A23CBB3D70>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /session/e0c613b78d6f992dc6b0b2d03d8f4c3b
2025-07-24 00:37:20,399 - WARNING - Retrying (Retry(total=1, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002A23CBB3E60>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /session/e0c613b78d6f992dc6b0b2d03d8f4c3b
2025-07-24 00:37:24,479 - WARNING - Retrying (Retry(total=0, connect=None, read=None, redirect=None, status=None)) after connection broken by 'NewConnectionError('<urllib3.connection.HTTPConnection object at 0x000002A23CC00320>: Failed to establish a new connection: [WinError 10061] 由于目标计算机积极拒绝，无法连接。')': /session/e0c613b78d6f992dc6b0b2d03d8f4c3b
