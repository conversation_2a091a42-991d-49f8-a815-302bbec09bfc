# 智慧中小学自动登录脚本 - 批量版使用说明

## 🚀 主要特点

- ✅ **纯手动验证码**：完全去除自动识别，只需手动拖拽滑块
- ✅ **批量处理**：支持多个账号批量登录
- ✅ **快速输入**：用户名密码快速输入，不再逐字模拟
- ✅ **txt文件导入**：支持从txt文件批量导入账号
- ✅ **姓名截图**：截图文件使用姓名命名，便于识别

## 📋 快速开始

### 1. 准备账号文件

创建 `accounts.txt` 文件，格式如下：
```
姓名 账号 密码
张三 *********** password123
李四 *********** mypassword456
王五 *********** secure password 789
```

**注意事项：**
- 每行一个账号
- 用空格分隔姓名、账号、密码
- 密码可以包含空格
- 以#开头的行为注释行

### 2. 运行脚本

**方式一：使用批处理文件**
```
双击运行 "启动脚本_批量版.bat"
```

**方式二：命令行运行**
```bash
python 智慧中小学自动登录.py
```

### 3. 选择账号来源

运行后会提示选择：
- 选择 `1`：使用配置文件 (config.py)
- 选择 `2`：使用txt文件 (accounts.txt) **推荐**

### 4. 手动操作验证码

- 脚本会自动填写用户名密码
- 出现验证码时，**手动拖拽滑块**到正确位置
- 脚本会自动检测验证完成状态
- 完成后自动进入下一个账号

## 🔧 操作流程

```
开始
 ↓
选择账号来源 (配置文件/txt文件)
 ↓
加载账号列表
 ↓
逐个处理账号:
 ├─ 打开浏览器
 ├─ 访问登录页面
 ├─ 快速输入用户名密码
 ├─ 点击登录按钮
 ├─ 【手动操作验证码】← 需要人工操作
 ├─ 检测登录成功
 ├─ 访问目标页面
 ├─ 截图保存 (使用姓名命名)
 └─ 关闭浏览器
 ↓
显示处理结果汇总
 ↓
结束
```

## 📁 输出文件

### 日志文件 (logs/)
- `smartedu_login_姓名_时间戳.log` - 每个账号的详细日志

### 截图文件 (screenshots/)
- `screenshot_姓名_时间戳.png` - 每个账号的页面截图

## ⚙️ 配置说明

### 批量处理配置
```python
BATCH_CONFIG = {
    "account_delay": 5,         # 账号间隔时间（秒）
    "continue_on_error": True,  # 遇到错误是否继续
    "save_individual_logs": True # 是否保存单独日志
}
```

### 验证码配置
```python
CAPTCHA_CONFIG = {
    "manual_mode": True,    # 手动模式
    "wait_time": 60,       # 等待时间（秒）
    "show_tips": True      # 显示操作提示
}
```

## ❓ 常见问题

### Q: 验证码操作超时怎么办？
A: 可以在配置中增加 `wait_time` 的值，给更多时间完成验证码。

### Q: 某个账号失败了，会影响其他账号吗？
A: 不会，脚本会继续处理下一个账号，最后显示所有结果。

### Q: 如何跳过某个账号？
A: 在txt文件中该行前面加 `#` 注释掉，或在配置文件中设置 `enabled: False`。

### Q: 截图文件在哪里？
A: 在 `screenshots/` 文件夹中，文件名格式为 `screenshot_姓名_时间戳.png`。

### Q: 如何查看详细日志？
A: 在 `logs/` 文件夹中，每个账号都有单独的日志文件。

## 🔒 注意事项

1. **网络稳定**：确保网络连接稳定
2. **专注操作**：批量处理时需要持续关注验证码操作
3. **合理间隔**：建议设置适当的账号间隔时间
4. **账号安全**：妥善保管账号文件，避免泄露

## 📞 技术支持

如遇问题：
1. 查看对应账号的日志文件
2. 检查账号信息是否正确
3. 确认网络连接正常
4. 验证Chrome浏览器版本
