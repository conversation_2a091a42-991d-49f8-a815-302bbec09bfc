#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ChromeDriver 辅助工具
用于自动下载和配置ChromeDriver
"""

import os
import sys
import requests
import zipfile
import json
import subprocess
from pathlib import Path

class ChromeDriverHelper:
    def __init__(self):
        self.chrome_version = None
        self.driver_version = None
        self.driver_path = None
        
    def get_chrome_version(self):
        """获取本地Chrome版本"""
        try:
            # Windows系统
            if sys.platform == "win32":
                import winreg
                # 尝试从注册表获取Chrome版本
                try:
                    key = winreg.OpenKey(winreg.HKEY_CURRENT_USER, r"Software\Google\Chrome\BLBeacon")
                    version, _ = winreg.QueryValueEx(key, "version")
                    winreg.CloseKey(key)
                    self.chrome_version = version
                    print(f"从注册表获取Chrome版本: {version}")
                    return version
                except:
                    pass
                
                # 尝试从文件版本获取
                chrome_paths = [
                    r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                    r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                    os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe")
                ]
                
                for chrome_path in chrome_paths:
                    if os.path.exists(chrome_path):
                        try:
                            result = subprocess.run([chrome_path, "--version"], 
                                                  capture_output=True, text=True, timeout=10)
                            if result.returncode == 0:
                                version = result.stdout.strip().split()[-1]
                                self.chrome_version = version
                                print(f"从命令行获取Chrome版本: {version}")
                                return version
                        except:
                            continue
            
            print("无法获取Chrome版本，将使用最新版本的ChromeDriver")
            return None
            
        except Exception as e:
            print(f"获取Chrome版本失败: {str(e)}")
            return None
    
    def get_compatible_driver_version(self):
        """获取兼容的ChromeDriver版本"""
        try:
            if not self.chrome_version:
                # 如果无法获取Chrome版本，使用最新的稳定版本
                url = "https://chromedriver.chromium.org/downloads"
                print("使用最新稳定版本的ChromeDriver")
                return "latest"
            
            # 提取主版本号
            major_version = self.chrome_version.split('.')[0]
            
            # Chrome 115+使用新的API
            if int(major_version) >= 115:
                url = f"https://googlechromelabs.github.io/chrome-for-testing/known-good-versions-with-downloads.json"
                response = requests.get(url, timeout=10)
                data = response.json()
                
                # 查找匹配的版本
                for version_info in data['versions']:
                    if version_info['version'].startswith(major_version + '.'):
                        if 'downloads' in version_info and 'chromedriver' in version_info['downloads']:
                            self.driver_version = version_info['version']
                            print(f"找到兼容的ChromeDriver版本: {self.driver_version}")
                            return self.driver_version
            else:
                # 旧版本Chrome使用旧API
                url = f"https://chromedriver.storage.googleapis.com/LATEST_RELEASE_{major_version}"
                response = requests.get(url, timeout=10)
                if response.status_code == 200:
                    self.driver_version = response.text.strip()
                    print(f"找到兼容的ChromeDriver版本: {self.driver_version}")
                    return self.driver_version
            
            print("无法找到兼容版本，使用最新版本")
            return "latest"
            
        except Exception as e:
            print(f"获取ChromeDriver版本失败: {str(e)}")
            return "latest"
    
    def download_chromedriver(self):
        """下载ChromeDriver"""
        try:
            # 确定下载URL
            if self.driver_version == "latest" or not self.driver_version:
                # 使用最新版本
                if sys.platform == "win32":
                    download_url = "https://storage.googleapis.com/chrome-for-testing-public/131.0.6778.85/win64/chromedriver-win64.zip"
                else:
                    download_url = "https://storage.googleapis.com/chrome-for-testing-public/131.0.6778.85/linux64/chromedriver-linux64.zip"
            else:
                # 使用特定版本
                if sys.platform == "win32":
                    download_url = f"https://storage.googleapis.com/chrome-for-testing-public/{self.driver_version}/win64/chromedriver-win64.zip"
                else:
                    download_url = f"https://storage.googleapis.com/chrome-for-testing-public/{self.driver_version}/linux64/chromedriver-linux64.zip"
            
            print(f"正在下载ChromeDriver: {download_url}")
            
            # 下载文件
            response = requests.get(download_url, timeout=30)
            response.raise_for_status()
            
            # 保存到临时文件
            zip_path = "chromedriver.zip"
            with open(zip_path, "wb") as f:
                f.write(response.content)
            
            print("ChromeDriver下载完成")
            
            # 解压文件
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(".")
            
            # 查找解压后的chromedriver文件
            if sys.platform == "win32":
                possible_paths = [
                    "chromedriver.exe",
                    "chromedriver-win64/chromedriver.exe",
                    "chromedriver-win32/chromedriver.exe"
                ]
            else:
                possible_paths = [
                    "chromedriver",
                    "chromedriver-linux64/chromedriver"
                ]
            
            for path in possible_paths:
                if os.path.exists(path):
                    # 移动到当前目录
                    final_name = "chromedriver.exe" if sys.platform == "win32" else "chromedriver"
                    if path != final_name:
                        if os.path.exists(final_name):
                            os.remove(final_name)
                        os.rename(path, final_name)
                    
                    # 设置执行权限（Linux/Mac）
                    if sys.platform != "win32":
                        os.chmod(final_name, 0o755)
                    
                    self.driver_path = os.path.abspath(final_name)
                    print(f"ChromeDriver已安装到: {self.driver_path}")
                    
                    # 清理临时文件
                    try:
                        os.remove(zip_path)
                        # 清理解压目录
                        import shutil
                        for item in os.listdir("."):
                            if item.startswith("chromedriver-") and os.path.isdir(item):
                                shutil.rmtree(item)
                    except:
                        pass
                    
                    return self.driver_path
            
            raise Exception("解压后未找到chromedriver文件")
            
        except Exception as e:
            print(f"下载ChromeDriver失败: {str(e)}")
            return None
    
    def setup_chromedriver(self):
        """设置ChromeDriver"""
        print("=" * 50)
        print("ChromeDriver 自动配置工具")
        print("=" * 50)
        
        # 检查是否已存在chromedriver
        existing_driver = None
        if sys.platform == "win32":
            if os.path.exists("chromedriver.exe"):
                existing_driver = "chromedriver.exe"
        else:
            if os.path.exists("chromedriver"):
                existing_driver = "chromedriver"
        
        if existing_driver:
            print(f"发现已存在的ChromeDriver: {existing_driver}")
            try:
                # 测试现有driver
                from selenium import webdriver
                from selenium.webdriver.chrome.options import Options
                
                options = Options()
                options.add_argument('--headless')
                options.add_argument('--no-sandbox')
                
                driver = webdriver.Chrome(executable_path=existing_driver, options=options)
                driver.get("https://www.baidu.com")
                driver.quit()
                
                print("现有ChromeDriver测试成功，无需重新下载")
                return os.path.abspath(existing_driver)
                
            except Exception as e:
                print(f"现有ChromeDriver测试失败: {str(e)}")
                print("将重新下载ChromeDriver")
        
        # 获取Chrome版本
        self.get_chrome_version()
        
        # 获取兼容的driver版本
        self.get_compatible_driver_version()
        
        # 下载ChromeDriver
        driver_path = self.download_chromedriver()
        
        if driver_path:
            print("✅ ChromeDriver配置成功！")
            print(f"路径: {driver_path}")
            return driver_path
        else:
            print("❌ ChromeDriver配置失败！")
            return None

def main():
    """主函数"""
    helper = ChromeDriverHelper()
    result = helper.setup_chromedriver()
    
    if result:
        print("\n现在可以运行主脚本了:")
        print("python 智慧中小学自动登录.py")
    else:
        print("\n请手动下载ChromeDriver:")
        print("1. 访问 https://chromedriver.chromium.org/downloads")
        print("2. 下载与你的Chrome版本匹配的ChromeDriver")
        print("3. 将chromedriver.exe放在脚本同一目录下")

if __name__ == "__main__":
    main()
