# 智慧中小学自动登录脚本（批量版）

这是一个功能完整的智慧中小学平台自动登录脚本，支持批量处理多个账号，具备以下特性：

## 🚀 主要功能

- ✅ 自动填写登录表单（快速输入）
- ✅ 手动操作滑块验证码（更稳定可靠）
- ✅ 批量处理多个账号
- ✅ 支持txt文件导入账号（格式：姓名 账号 密码）
- ✅ 截图使用姓名命名
- ✅ 反作弊措施（模拟人类操作行为）
- ✅ 完善的错误处理和重试机制
- ✅ 详细的日志记录（支持单独日志）
- ✅ 自动截图指定页面
- ✅ 可配置的参数设置
- ✅ 智能进度显示和结果汇总

## 📋 系统要求

- Python 3.7+
- Chrome浏览器
- ChromeDriver（会自动下载）
- 稳定的网络连接

## 🛠️ 安装步骤

### 1. 克隆或下载项目文件

确保以下文件在同一目录中：
- `智慧中小学自动登录.py` - 主脚本
- `config.py` - 配置文件（或使用config_example.py作为模板）
- `accounts.txt` - 账号文件（可选，或使用accounts_example.txt作为模板）
- `启动脚本_批量版.bat` - Windows启动脚本
- `README.md` - 说明文档

### 2. 安装Python依赖

```bash
pip install selenium pillow
```

### 3. 准备账号信息

**方式一：使用配置文件**
1. 复制 `config_example.py` 为 `config.py`
2. 修改其中的账号信息

**方式二：使用txt文件（推荐）**
1. 复制 `accounts_example.txt` 为 `accounts.txt`
2. 按格式填写账号信息：`姓名 账号 密码`

### 4. 安装ChromeDriver

脚本会自动处理ChromeDriver的下载，或运行 `chromedriver_helper.py` 手动配置。

## ⚙️ 配置说明

在运行脚本前，请根据需要修改 `config.py` 文件：

### 批量账号配置
```python
ACCOUNTS_CONFIG = [
    {
        "username": "账号1的用户名",
        "password": "账号1的密码",
        "name": "张三的账号",  # 账号备注名称
        "enabled": True       # 是否启用此账号
    },
    {
        "username": "账号2的用户名",
        "password": "账号2的密码",
        "name": "李四的账号",
        "enabled": True
    },
    # 可以继续添加更多账号...
]
```

### 验证码配置（手动模式）
```python
CAPTCHA_CONFIG = {
    "manual_mode": True,        # 启用手动模式
    "wait_time": 60,           # 手动操作等待时间（秒）
    "show_tips": True          # 是否显示操作提示
}
```

### 其他配置选项
- `BROWSER_CONFIG`: 浏览器设置（窗口大小、无头模式等）
- `RETRY_CONFIG`: 重试次数和延迟设置
- `BATCH_CONFIG`: 批量处理设置（账号间隔、错误处理等）
- `LOG_CONFIG`: 日志级别和保存设置
- `SCREENSHOT_CONFIG`: 截图保存设置

## 🚀 使用方法

### 基本使用

```bash
python 智慧中小学自动登录.py
```

### 运行流程

**单账号模式：**
1. **初始化浏览器** - 启动Chrome浏览器并配置反检测
2. **访问登录页面** - 导航到智慧中小学登录页面
3. **填写表单** - 自动输入用户名和密码
4. **手动验证码** - 等待用户手动完成滑块验证码
5. **登录验证** - 检查登录是否成功
6. **访问目标页面** - 跳转到指定的培训页面
7. **截图保存** - 对页面上半部分进行截图

**批量模式：**
1. **读取账号列表** - 加载所有启用的账号
2. **逐个处理账号** - 按顺序处理每个账号
3. **账号间隔等待** - 避免频繁操作被检测
4. **结果汇总显示** - 显示所有账号的处理结果

## 📁 输出文件

脚本运行后会生成以下文件夹和文件：

```
项目目录/
├── logs/                    # 日志文件夹
│   └── smartedu_login_*.log # 详细运行日志
├── screenshots/             # 截图文件夹
│   └── screenshot_upper_*.png # 页面截图
└── ...
```

## 🔧 高级功能

### 反作弊措施

- 随机延迟模拟人类操作
- 浏览器指纹伪装
- 手动验证码操作（避免自动化检测）
- 账号间隔处理（避免批量检测）

### 错误处理

- 自动重试机制（默认2次）
- 详细错误日志记录
- 网络异常处理
- 批量处理错误隔离
- 单独账号日志记录

### 日志系统

- 多级别日志记录（DEBUG, INFO, WARNING, ERROR）
- 同时输出到控制台和文件
- 时间戳和详细错误信息
- 可配置日志保存路径

## ⚠️ 重要说明

### 验证码处理方式
为了提高成功率和稳定性，本版本采用手动验证码操作：

1. **手动操作**: 脚本会自动填写表单，验证码需要用户手动完成
2. **智能等待**: 脚本会智能检测验证码完成状态
3. **操作提示**: 提供清晰的操作指引和剩余时间提示
4. **多次重试**: 支持验证码失败后的重试机制

### 批量处理建议
1. **账号准备**: 确保所有账号信息正确，密码有效
2. **网络环境**: 确保网络连接稳定，避免中途断网
3. **专注操作**: 批量处理时需要持续关注验证码操作
4. **合理间隔**: 建议设置适当的账号间隔时间（默认5秒）
5. **错误处理**: 建议启用"遇到错误继续处理"选项

## 🐛 常见问题

### Q: 脚本运行失败怎么办？
A: 查看logs文件夹中的日志文件，根据错误信息进行排查。

### Q: 手动验证码操作超时？
A: 检查网络连接，确保在规定时间内完成验证码拖拽。可以在配置中增加等待时间。

### Q: 浏览器启动失败？
A: 确保Chrome浏览器已正确安装，或尝试更新ChromeDriver。

### Q: 登录成功但截图失败？
A: 检查目标页面是否正确加载，网络是否稳定。

## 📞 技术支持

如遇到问题，请：
1. 查看详细日志文件
2. 检查配置文件设置
3. 确认网络连接状态
4. 验证账号密码正确性

## 📄 免责声明

本脚本仅供学习和研究使用，使用者需遵守相关网站的使用条款和法律法规。作者不承担因使用本脚本而产生的任何责任。
