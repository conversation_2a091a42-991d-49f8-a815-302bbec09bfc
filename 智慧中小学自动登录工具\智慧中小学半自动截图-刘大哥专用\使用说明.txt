 使用指南

 账号管理

1. 添加账号
   - 点击"➕ 添加账号"按钮
   - 填写姓名、账号、密码
   - 选择是否启用该账号

2. 编辑账号
   - 在账号列表中选择要编辑的账号
   - 点击"✏️ 编辑"按钮
   - 修改账号信息

3. 删除账号
   - 选择要删除的账号
   - 点击"🗑️ 删除"按钮
   - 确认删除操作

4. 批量导入
   - 点击"📁 导入"按钮
   - 选择txt格式的账号文件
   - 文件格式：每行一个账号，格式为 `姓名 账号 密码`

 执行任务

1. 设置倒计时
   - 在右侧设置区域修改"验证码倒计时(秒)"
   - 默认为10秒，可根据需要调整

2. 开始执行
   - 确保已添加并启用账号
   - 点击"🚀 开始执行"按钮
   - 程序将自动处理所有启用的账号

3. 验证码操作
   - 当出现验证码时，程序会显示倒计时
   - 在倒计时期间手动完成验证码操作
   - 倒计时结束后程序自动继续

4. 控制操作
   - 暂停/继续：点击"⏸️ 暂停"可暂停执行，再次点击继续
   - 停止：点击"⏹️ 停止"立即停止所有操作

 查看结果

1. 实时日志
   - 左下角日志区域显示详细的操作过程
   - 包括成功、失败、错误等信息

2. 执行状态
   - 右侧状态区域显示当前执行状态
   - 进度条显示整体执行进度

3. 截图文件
   - 成功登录的账号会自动截图
   - 截图保存在 `screenshots` 文件夹
   - 文件名格式：`姓名.png`

 ⚙️ 配置说明

 倒计时设置

- 验证码倒计时时间可在界面右侧调整
- 建议设置10-30秒，根据个人操作速度调整

 账号文件格式

导入的txt文件应遵循以下格式：

```
张三 13800138000 password123
李四 13900139000 mypassword
王五 13700137000 123456
```

 日志文件

- 详细日志保存在 `logs` 文件夹
- 每个账号都有独立的日志文件
- 可用于问题排查和分析