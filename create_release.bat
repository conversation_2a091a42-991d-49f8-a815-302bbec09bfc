@echo off
chcp 65001 >nul
echo ========================================
echo 智慧中小学自动登录工具 - 完整发布脚本
echo ========================================
echo.

echo 🔍 检查环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误：未找到Python环境
    pause
    exit /b 1
)

echo ✅ Python环境正常
echo.

echo 📦 安装打包依赖...
pip install pyinstaller >nul 2>&1

echo.
echo 🏗️ 生成打包配置...
python build_spec.py

echo.
echo 📦 开始打包程序...
echo ----------------------------------------
pyinstaller gui_app.spec --clean

echo.
if exist "dist\智慧中小学自动登录工具.exe" (
    echo ✅ 打包成功！
    echo.
    
    echo 📁 创建发布包...
    if exist "release" rmdir /s /q "release"
    mkdir "release"
    
    echo 复制主程序...
    copy "dist\智慧中小学自动登录工具.exe" "release\"
    
    echo 复制文档...
    copy "使用说明_GUI版.md" "release\"
    copy "requirements.txt" "release\"
    
    echo 复制安装脚本...
    copy "install_dependencies.bat" "release\"
    copy "start_gui.bat" "release\"
    
    echo 创建目录结构...
    mkdir "release\screenshots" 2>nul
    mkdir "release\logs" 2>nul
    
    echo 创建示例账号文件...
    echo # 账号文件示例 > "release\accounts_example.txt"
    echo # 格式：姓名 账号 密码 >> "release\accounts_example.txt"
    echo # 张三 *********** password123 >> "release\accounts_example.txt"
    echo # 李四 *********** mypassword >> "release\accounts_example.txt"
    
    echo.
    echo 🎉 发布包创建完成！
    echo ========================================
    echo 📂 发布文件夹：release\
    echo 📋 包含文件：
    echo    - 智慧中小学自动登录工具.exe （主程序）
    echo    - 使用说明_GUI版.md （使用说明）
    echo    - install_dependencies.bat （依赖安装）
    echo    - start_gui.bat （启动脚本）
    echo    - accounts_example.txt （账号文件示例）
    echo    - screenshots\ （截图文件夹）
    echo    - logs\ （日志文件夹）
    echo ========================================
    echo.
    echo 💡 使用提示：
    echo 1. 将整个release文件夹分发给用户
    echo 2. 用户首次使用需运行install_dependencies.bat
    echo 3. 然后可直接运行主程序或start_gui.bat
    echo.
    
    echo 🚀 是否立即测试程序？(Y/N)
    set /p test_choice=
    if /i "%test_choice%"=="Y" (
        echo 启动测试...
        cd release
        "智慧中小学自动登录工具.exe"
        cd ..
    )
    
) else (
    echo ❌ 打包失败！
    echo 请检查错误信息并重试
)

echo.
pause
